import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'theme/app_theme.dart';
import 'screens/onboarding_screen.dart';
import 'screens/camera_screen.dart';
import 'screens/editing_screen.dart';
import 'screens/export_screen.dart';
import 'screens/learning_center_screen.dart';

/// Aplicativo principal do FotosApetitosas
class FotosApetitosasApp extends StatelessWidget {
  const FotosApetitosasApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Providers serão adicionados aqui conforme necessário
      ],
      child: MaterialApp.router(
        title: 'FotosApetitosas',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        routerConfig: _router,
      ),
    );
  }
}

/// Configuração de rotas usando GoRouter
final GoRouter _router = GoRouter(
  initialLocation: '/onboarding',
  routes: [
    // Rota do Onboarding
    GoRoute(
      path: '/onboarding',
      name: 'onboarding',
      builder: (context, state) => const OnboardingScreen(),
    ),

    // Rota principal da câmera
    GoRoute(
      path: '/',
      name: 'camera',
      builder: (context, state) => const CameraScreen(),
    ),

    // Rota de edição
    GoRoute(
      path: '/editing',
      name: 'editing',
      builder: (context, state) {
        final imagePath = state.extra as String?;
        return EditingScreen(imagePath: imagePath);
      },
    ),

    // Rota de exportação
    GoRoute(
      path: '/export',
      name: 'export',
      builder: (context, state) {
        final imagePath = state.extra as String?;
        return ExportScreen(imagePath: imagePath);
      },
    ),

    // Rota do centro de aprendizado
    GoRoute(
      path: '/learning',
      name: 'learning',
      builder: (context, state) => const LearningCenterScreen(),
    ),
  ],

  // Tratamento de erros de navegação
  errorBuilder: (context, state) => Scaffold(
    appBar: AppBar(
      title: const Text('Erro'),
    ),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Página não encontrada',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'A página "${state.uri}" não existe.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/'),
            child: const Text('Voltar ao Início'),
          ),
        ],
      ),
    ),
  ),
);

/// Extensões úteis para navegação
extension AppNavigation on BuildContext {
  /// Navega para a tela da câmera
  void goToCamera() => go('/');

  /// Navega para a tela de edição
  void goToEditing(String? imagePath) => go('/editing', extra: imagePath);

  /// Navega para a tela de exportação
  void goToExport(String? imagePath) => go('/export', extra: imagePath);

  /// Navega para o centro de aprendizado
  void goToLearning() => go('/learning');

  /// Navega para o onboarding
  void goToOnboarding() => go('/onboarding');

  /// Volta para a tela anterior
  void goBack() => pop();
}
