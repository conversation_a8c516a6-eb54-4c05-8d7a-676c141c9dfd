# FotosApetitosas - MVP Flutter

App para ajudar confeiteiros a tirarem fotos profissionais de seus doces com guias inteligentes e ferramentas de edição otimizadas.

## 🎯 Fase 1 - Concluída ✅

### ✅ Configuração do Projeto
- [x] Projeto Flutter criado com FVM
- [x] Dependências principais instaladas
- [x] Estrutura de pastas organizada
- [x] Configuração de orientação (retrato)

### ✅ Design System iOS Nativo
- [x] Paleta de cores inspirada em doces e confeitaria
- [x] Tipografia seguindo diretrizes do iOS
- [x] Componentes com estilo iOS (botões, cards, sliders)
- [x] Sistema de espaçamento e bordas consistente

### ✅ Arquitetura e Navegação
- [x] GoRouter configurado para navegação
- [x] Estrutura de telas organizada
- [x] Extensões de navegação para facilitar uso
- [x] Tratamento de erros de navegação

### ✅ Telas Implementadas

#### 1. Onboarding Screen
- [x] 4 slides educativos com animações
- [x] Solicitação de permissões (câmera e galeria)
- [x] Navegação fluida entre slides
- [x] Integração com SharedPreferences

#### 2. Camera Screen
- [x] Preview da câmera em tempo real
- [x] Guias de composição (Regra dos Terços)
- [x] Controles de flash e troca de câmera
- [x] Captura de foto e seleção da galeria
- [x] Toggle para mostrar/ocultar guias

#### 3. Editing Screen
- [x] Preview da imagem selecionada
- [x] 4 sliders de ajuste (Brilho, Contraste, Saturação, Nitidez)
- [x] 5 presets otimizados para doces
- [x] Sistema de undo/redo com histórico
- [x] Botão de reset para valores originais

#### 4. Export Screen
- [x] Preview final da imagem editada
- [x] Salvamento na galeria do dispositivo
- [x] Feedback visual de sucesso/erro
- [x] Opções para nova foto ou editar novamente

#### 5. Learning Center Screen
- [x] 4 abas educativas (Fundamentos, Por Tipo, Erros Comuns, Inspiração)
- [x] Conteúdo educativo específico para fotografia de doces
- [x] Animações e layout responsivo
- [x] Dicas organizadas por tipo de doce

## 🛠️ Tecnologias Utilizadas

### Core
- **Flutter 3.5.3** (gerenciado via FVM)
- **Dart** para toda a lógica

### Packages Principais
- `camera` - Acesso à câmera do dispositivo
- `image_picker` - Seleção de imagens da galeria
- `gallery_saver` - Salvamento na galeria
- `go_router` - Navegação declarativa
- `provider` - Gerenciamento de estado
- `flutter_animate` - Animações fluidas
- `permission_handler` - Gerenciamento de permissões
- `shared_preferences` - Armazenamento local

### Design
- `Material 3` com customizações iOS
- Paleta de cores temática (rosa doce, laranja caramelo, verde menta)
- Componentes seguindo Apple Human Interface Guidelines

## 📱 Funcionalidades Implementadas

### ✅ Captura e Seleção
- Câmera integrada com preview em tempo real
- Seleção de fotos da galeria
- Guias visuais de composição (Regra dos Terços)
- Controles de flash e troca de câmera

### ✅ Edição Básica
- Ajustes manuais: Brilho, Contraste, Saturação, Nitidez
- Presets otimizados: "Doce Vivo", "Chocolate Profundo", "Fresco & Leve", "Clássico", "Vintage Doce"
- Sistema de histórico com undo/redo
- Preview em tempo real das alterações

### ✅ Exportação
- Salvamento em alta qualidade (90% JPEG)
- Feedback visual de progresso e sucesso
- Integração com galeria do sistema

### ✅ Educação Integrada
- Centro de aprendizado com 4 seções
- Dicas específicas por tipo de doce
- Guia de erros comuns e soluções
- Fundamentos de fotografia explicados

## 🎨 Design Highlights

### Paleta de Cores
- **Primary**: Rosa doce (#E91E63)
- **Secondary**: Laranja caramelo (#FF9800)
- **Accent**: Verde menta (#8BC34A)
- **Background**: Branco açúcar (#FAFAFA)

### Componentes iOS
- Botões com bordas arredondadas (12px)
- Cards com sombras suaves (16px radius)
- Sliders com design nativo iOS
- Navegação estilo iOS com gestos

## 📁 Estrutura do Projeto

```
lib/
├── main.dart                 # Ponto de entrada
├── app.dart                  # Configuração do app e rotas
├── screens/                  # Telas principais
│   ├── onboarding_screen.dart
│   ├── camera_screen.dart
│   ├── editing_screen.dart
│   ├── export_screen.dart
│   └── learning_center_screen.dart
├── theme/                    # Sistema de design
│   ├── colors.dart
│   └── app_theme.dart
├── widgets/                  # Componentes reutilizáveis
├── models/                   # Modelos de dados
├── services/                 # Serviços e lógica de negócio
└── utils/                    # Utilitários
```

## 🚀 Como Executar

1. **Instalar FVM** (se não tiver):
   ```bash
   dart pub global activate fvm
   ```

2. **Configurar Flutter**:
   ```bash
   fvm use stable
   ```

3. **Instalar dependências**:
   ```bash
   fvm flutter pub get
   ```

4. **Executar o app**:
   ```bash
   fvm flutter run
   ```

## 📋 Próximos Passos (Fase 2)

### Funcionalidades Core
- [ ] Implementar processamento real de imagem nos sliders
- [ ] Adicionar IA para detecção de objetos (TensorFlow Lite)
- [ ] Sistema de guias inteligentes em tempo real
- [ ] Análise automática de iluminação
- [ ] Sugestões contextuais baseadas no tipo de doce

### Melhorias de UX
- [ ] Animações de transição entre telas
- [ ] Feedback háptico para interações
- [ ] Tutorial interativo na primeira utilização
- [ ] Sistema de conquistas e gamificação

### Performance
- [ ] Otimização de processamento de imagem
- [ ] Cache inteligente de presets
- [ ] Compressão otimizada para diferentes qualidades

## 🧪 Testes

Para executar os testes:
```bash
fvm flutter test
```

## 📄 Licença

Este projeto é um MVP para demonstração de conceito.

---

**Desenvolvido com ❤️ para confeiteiros que querem criar fotos incríveis de seus doces!**
