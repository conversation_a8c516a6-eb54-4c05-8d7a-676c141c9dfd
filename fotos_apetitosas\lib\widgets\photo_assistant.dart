import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:vibration/vibration.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';
import '../services/image_processing_service.dart';
import '../services/composition_analysis_service.dart';

/// Widget assistente fotográfico inteligente
class PhotoAssistant extends StatefulWidget {
  final String? currentImagePath;
  final VoidCallback? onTipTap;
  final bool isVisible;

  const PhotoAssistant({
    super.key,
    this.currentImagePath,
    this.onTipTap,
    this.isVisible = true,
  });

  @override
  State<PhotoAssistant> createState() => _PhotoAssistantState();
}

class _PhotoAssistantState extends State<PhotoAssistant>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  
  LightingAnalysis? _lightingAnalysis;
  CompositionAnalysis? _compositionAnalysis;
  SweetType? _detectedSweetType;
  
  bool _isAnalyzing = false;
  int _currentTipIndex = 0;
  List<AssistantTip> _currentTips = [];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    if (widget.isVisible) {
      _slideController.forward();
    }
    
    _generateInitialTips();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(PhotoAssistant oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _slideController.forward();
      } else {
        _slideController.reverse();
      }
    }
    
    if (widget.currentImagePath != oldWidget.currentImagePath &&
        widget.currentImagePath != null) {
      _analyzeCurrentImage();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeInOut,
      )),
      child: Container(
        margin: const EdgeInsets.all(AppSpacing.md),
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: AppColors.surface.withOpacity(0.95),
          borderRadius: BorderRadius.circular(AppRadius.lg),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowMedium,
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: AppSpacing.sm),
            _buildCurrentTip(),
            if (_isAnalyzing) _buildAnalyzingIndicator(),
            if (_lightingAnalysis != null) _buildLightingInfo(),
            if (_compositionAnalysis != null) _buildCompositionInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + (_pulseController.value * 0.1),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.assistant,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Assistente Fotográfico',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
              if (_detectedSweetType != null)
                Text(
                  '${_detectedSweetType!.emoji} ${_detectedSweetType!.name} detectado',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ),
        IconButton(
          onPressed: _nextTip,
          icon: const Icon(Icons.refresh, size: 20),
          color: AppColors.primary,
        ),
      ],
    );
  }

  Widget _buildCurrentTip() {
    if (_currentTips.isEmpty) {
      return const SizedBox.shrink();
    }

    final currentTip = _currentTips[_currentTipIndex];
    
    return GestureDetector(
      onTap: () {
        _provideFeedback();
        widget.onTipTap?.call();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: currentTip.color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppRadius.sm),
          border: Border.all(
            color: currentTip.color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              currentTip.icon,
              color: currentTip.color,
              size: 20,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                currentTip.message,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: currentTip.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate()
        .fadeIn(duration: 300.ms)
        .slideX(begin: 0.3, duration: 300.ms);
  }

  Widget _buildAnalyzingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Text(
            'Analisando foto...',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLightingInfo() {
    if (_lightingAnalysis == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: _lightingAnalysis!.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.sm),
      ),
      child: Row(
        children: [
          Icon(
            _lightingAnalysis!.icon,
            color: _lightingAnalysis!.color,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.xs),
          Expanded(
            child: Text(
              _lightingAnalysis!.suggestion,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _lightingAnalysis!.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompositionInfo() {
    if (_compositionAnalysis == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: _compositionAnalysis!.qualityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.sm),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _compositionAnalysis!.qualityIcon,
                color: _compositionAnalysis!.qualityColor,
                size: 16,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                'Composição: ${_compositionAnalysis!.scorePercentage}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _compositionAnalysis!.qualityColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          if (_compositionAnalysis!.suggestions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: AppSpacing.xs),
              child: Text(
                _compositionAnalysis!.suggestions.first,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _compositionAnalysis!.qualityColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _generateInitialTips() {
    _currentTips = [
      AssistantTip(
        message: 'Use a regra dos terços para uma composição mais interessante',
        icon: Icons.grid_on,
        color: AppColors.primary,
        type: TipType.composition,
      ),
      AssistantTip(
        message: 'Procure luz natural próxima a uma janela',
        icon: Icons.wb_sunny,
        color: AppColors.secondary,
        type: TipType.lighting,
      ),
      AssistantTip(
        message: 'Mantenha o fundo limpo e sem distrações',
        icon: Icons.crop_free,
        color: AppColors.accent,
        type: TipType.background,
      ),
      AssistantTip(
        message: 'Toque na tela para focar no seu doce',
        icon: Icons.center_focus_strong,
        color: AppColors.info,
        type: TipType.focus,
      ),
    ];
  }

  void _nextTip() {
    setState(() {
      _currentTipIndex = (_currentTipIndex + 1) % _currentTips.length;
    });
    _provideFeedback();
  }

  Future<void> _analyzeCurrentImage() async {
    if (widget.currentImagePath == null) return;

    setState(() {
      _isAnalyzing = true;
    });

    try {
      // Análises paralelas
      final futures = await Future.wait([
        ImageProcessingService().analyzeLighting(widget.currentImagePath!),
        CompositionAnalysisService().analyzeComposition(widget.currentImagePath!),
        ImageProcessingService().detectSweetType(widget.currentImagePath!),
      ]);

      setState(() {
        _lightingAnalysis = futures[0] as LightingAnalysis;
        _compositionAnalysis = futures[1] as CompositionAnalysis;
        _detectedSweetType = futures[2] as SweetType;
        _isAnalyzing = false;
      });

      _updateTipsBasedOnAnalysis();
    } catch (e) {
      debugPrint('Erro na análise: $e');
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  void _updateTipsBasedOnAnalysis() {
    final newTips = <AssistantTip>[];

    // Adiciona dicas baseadas na análise de iluminação
    if (_lightingAnalysis != null) {
      newTips.add(AssistantTip(
        message: _lightingAnalysis!.suggestion,
        icon: _lightingAnalysis!.icon,
        color: _lightingAnalysis!.color,
        type: TipType.lighting,
      ));
    }

    // Adiciona dicas baseadas na composição
    if (_compositionAnalysis != null && _compositionAnalysis!.suggestions.isNotEmpty) {
      for (final suggestion in _compositionAnalysis!.suggestions) {
        newTips.add(AssistantTip(
          message: suggestion,
          icon: _compositionAnalysis!.qualityIcon,
          color: _compositionAnalysis!.qualityColor,
          type: TipType.composition,
        ));
      }
    }

    // Adiciona dicas específicas do tipo de doce
    if (_detectedSweetType != null) {
      for (final tip in _detectedSweetType!.tips) {
        newTips.add(AssistantTip(
          message: tip,
          icon: Icons.lightbulb_outline,
          color: AppColors.primary,
          type: TipType.sweetSpecific,
        ));
      }
    }

    if (newTips.isNotEmpty) {
      setState(() {
        _currentTips = newTips;
        _currentTipIndex = 0;
      });
    }
  }

  Future<void> _provideFeedback() async {
    // Feedback háptico
    if (await Vibration.hasVibrator() ?? false) {
      Vibration.vibrate(duration: 50);
    }
  }
}

/// Modelo para dicas do assistente
class AssistantTip {
  final String message;
  final IconData icon;
  final Color color;
  final TipType type;

  const AssistantTip({
    required this.message,
    required this.icon,
    required this.color,
    required this.type,
  });
}

/// Tipos de dicas
enum TipType {
  composition,
  lighting,
  background,
  focus,
  sweetSpecific,
}
