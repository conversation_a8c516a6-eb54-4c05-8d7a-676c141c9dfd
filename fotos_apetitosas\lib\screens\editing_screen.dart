import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vibration/vibration.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';
import '../services/image_processing_service.dart';
import '../widgets/photo_assistant.dart';

/// Tela de edição básica com ajustes e presets
class EditingScreen extends StatefulWidget {
  final String? imagePath;

  const EditingScreen({
    super.key,
    required this.imagePath,
  });

  @override
  State<EditingScreen> createState() => _EditingScreenState();
}

class _EditingScreenState extends State<EditingScreen> {
  // Valores dos ajustes
  double _brightness = 0.0;
  double _contrast = 0.0;
  double _saturation = 0.0;
  double _sharpness = 0.0;

  // Histórico para undo/redo
  final List<Map<String, double>> _history = [];
  int _historyIndex = -1;

  // Processamento de imagem
  String? _processedImagePath;
  bool _isProcessing = false;
  bool _showAssistant = true;

  @override
  void initState() {
    super.initState();
    _processedImagePath = widget.imagePath;
    _saveToHistory();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imagePath == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edição')),
        body: const Center(
          child: Text('Nenhuma imagem selecionada'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Editar Foto'),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.close),
        ),
        actions: [
          // Desfazer
          IconButton(
            onPressed: _canUndo() ? _undo : null,
            icon: const Icon(Icons.undo),
          ),

          // Refazer
          IconButton(
            onPressed: _canRedo() ? _redo : null,
            icon: const Icon(Icons.redo),
          ),

          // Redefinir
          IconButton(
            onPressed: _reset,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Preview da imagem
          Expanded(
            flex: 3,
            child: _buildImagePreview(),
          ),

          // Presets
          _buildPresets(),

          // Controles de ajuste
          Expanded(
            flex: 2,
            child: _buildAdjustmentControls(),
          ),

          // Botão de continuar
          _buildContinueButton(),
        ],
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.md),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppRadius.md),
            child: Image.file(
              File(_processedImagePath ?? widget.imagePath!),
              fit: BoxFit.contain,
            ),
          ),

          // Indicador de processamento
          if (_isProcessing)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(AppRadius.md),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                ),
              ),
            ),

          // Assistente fotográfico
          if (_showAssistant)
            Positioned(
              bottom: AppSpacing.sm,
              left: AppSpacing.sm,
              right: AppSpacing.sm,
              child: PhotoAssistant(
                currentImagePath: _processedImagePath ?? widget.imagePath,
                isVisible: _showAssistant,
                onTipTap: () => _provideFeedback(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPresets() {
    final presets = [
      PresetData('Original', Colors.grey, {}),
      PresetData('Doce Vivo', AppColors.primary, {
        'brightness': 10.0,
        'contrast': 15.0,
        'saturation': 25.0,
        'sharpness': 10.0,
      }),
      PresetData('Chocolate', const Color(0xFF8B4513), {
        'brightness': -5.0,
        'contrast': 20.0,
        'saturation': 10.0,
        'sharpness': 15.0,
      }),
      PresetData('Fresco', AppColors.accent, {
        'brightness': 15.0,
        'contrast': 5.0,
        'saturation': 15.0,
        'sharpness': 5.0,
      }),
      PresetData('Vintage', const Color(0xFFDEB887), {
        'brightness': 5.0,
        'contrast': 10.0,
        'saturation': -10.0,
        'sharpness': 0.0,
      }),
    ];

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: presets.length,
        itemBuilder: (context, index) {
          final preset = presets[index];
          return GestureDetector(
            onTap: () => _applyPreset(preset.values),
            child: Container(
              width: 70,
              margin: const EdgeInsets.only(right: AppSpacing.sm),
              decoration: BoxDecoration(
                color: preset.color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppRadius.sm),
                border: Border.all(
                  color: preset.color,
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.filter_vintage,
                    color: preset.color,
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    preset.name,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAdjustmentControls() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        children: [
          _buildSlider(
            'Brilho',
            _brightness,
            -100,
            100,
            Icons.brightness_6,
            (value) => _updateSliderWithDebounce('brightness', value),
          ),
          _buildSlider(
            'Contraste',
            _contrast,
            -100,
            100,
            Icons.contrast,
            (value) => _updateSliderWithDebounce('contrast', value),
          ),
          _buildSlider(
            'Saturação',
            _saturation,
            -100,
            100,
            Icons.palette,
            (value) => _updateSliderWithDebounce('saturation', value),
          ),
          _buildSlider(
            'Nitidez',
            _sharpness,
            0,
            200,
            Icons.tune,
            (value) => _updateSliderWithDebounce('sharpness', value),
          ),
        ],
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    IconData icon,
    ValueChanged<double> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(width: AppSpacing.sm),
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                  ),
            ),
          ),
          Expanded(
            child: Slider(
              value: value,
              min: min,
              max: max,
              onChanged: onChanged,
              onChangeEnd: (_) => _saveToHistory(),
              activeColor: AppColors.primary,
              inactiveColor: Colors.grey,
            ),
          ),
          SizedBox(
            width: 40,
            child: Text(
              value.round().toString(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _isProcessing
              ? null
              : () => context.go('/export',
                  extra: _processedImagePath ?? widget.imagePath),
          child: _isProcessing
              ? const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text('Processando...'),
                  ],
                )
              : const Text('Continuar'),
        ),
      ),
    );
  }

  void _applyPreset(Map<String, double> values) {
    setState(() {
      _brightness = values['brightness'] ?? 0.0;
      _contrast = values['contrast'] ?? 0.0;
      _saturation = values['saturation'] ?? 0.0;
      _sharpness = values['sharpness'] ?? 0.0;
    });
    _saveToHistory();
    _processImage();
    _provideFeedback();
  }

  void _saveToHistory() {
    final state = {
      'brightness': _brightness,
      'contrast': _contrast,
      'saturation': _saturation,
      'sharpness': _sharpness,
    };

    // Remove estados futuros se estamos no meio do histórico
    if (_historyIndex < _history.length - 1) {
      _history.removeRange(_historyIndex + 1, _history.length);
    }

    _history.add(Map.from(state));
    _historyIndex = _history.length - 1;

    // Limita o histórico a 20 estados
    if (_history.length > 20) {
      _history.removeAt(0);
      _historyIndex--;
    }
  }

  bool _canUndo() => _historyIndex > 0;
  bool _canRedo() => _historyIndex < _history.length - 1;

  void _undo() {
    if (_canUndo()) {
      _historyIndex--;
      _loadFromHistory();
    }
  }

  void _redo() {
    if (_canRedo()) {
      _historyIndex++;
      _loadFromHistory();
    }
  }

  void _loadFromHistory() {
    final state = _history[_historyIndex];
    setState(() {
      _brightness = state['brightness']!;
      _contrast = state['contrast']!;
      _saturation = state['saturation']!;
      _sharpness = state['sharpness']!;
    });
  }

  void _reset() {
    setState(() {
      _brightness = 0.0;
      _contrast = 0.0;
      _saturation = 0.0;
      _sharpness = 0.0;
      _processedImagePath = widget.imagePath;
    });
    _saveToHistory();
    _provideFeedback();
  }

  /// Processa a imagem com os ajustes atuais
  Future<void> _processImage() async {
    if (widget.imagePath == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final processedPath = await ImageProcessingService().processImage(
        imagePath: widget.imagePath!,
        brightness: _brightness,
        contrast: _contrast,
        saturation: _saturation,
        sharpness: _sharpness,
      );

      setState(() {
        _processedImagePath = processedPath;
        _isProcessing = false;
      });
    } catch (e) {
      debugPrint('Erro ao processar imagem: $e');
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// Fornece feedback háptico ao usuário
  Future<void> _provideFeedback() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        Vibration.vibrate(duration: 50);
      }
    } catch (e) {
      debugPrint('Erro no feedback háptico: $e');
    }
  }

  /// Atualiza os sliders com debounce para performance
  void _updateSliderWithDebounce(String type, double value) {
    setState(() {
      switch (type) {
        case 'brightness':
          _brightness = value;
          break;
        case 'contrast':
          _contrast = value;
          break;
        case 'saturation':
          _saturation = value;
          break;
        case 'sharpness':
          _sharpness = value;
          break;
      }
    });

    // Debounce para evitar processamento excessivo
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _processImage();
      }
    });
  }
}

/// Modelo para dados de preset
class PresetData {
  final String name;
  final Color color;
  final Map<String, double> values;

  const PresetData(this.name, this.color, this.values);
}
