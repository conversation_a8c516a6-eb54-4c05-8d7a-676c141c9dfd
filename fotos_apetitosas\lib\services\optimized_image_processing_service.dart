import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

/// Serviço otimizado para processamento de imagem usando isolates
class OptimizedImageProcessingService {
  static final OptimizedImageProcessingService _instance =
      OptimizedImageProcessingService._internal();
  factory OptimizedImageProcessingService() => _instance;
  OptimizedImageProcessingService._internal();

  /// Cache de imagens processadas para evitar reprocessamento
  final Map<String, String> _processedImageCache = {};

  /// Processa imagem de forma otimizada usando isolate
  Future<String> processImageOptimized({
    required String imagePath,
    double brightness = 0.0,
    double contrast = 0.0,
    double saturation = 0.0,
    double sharpness = 0.0,
  }) async {
    try {
      // Cria uma chave única para o cache baseada nos parâmetros
      final cacheKey = _generateCacheKey(
          imagePath, brightness, contrast, saturation, sharpness);

      // Verifica se já temos essa imagem processada no cache
      if (_processedImageCache.containsKey(cacheKey)) {
        final cachedPath = _processedImageCache[cacheKey]!;
        if (await File(cachedPath).exists()) {
          return cachedPath;
        } else {
          _processedImageCache.remove(cacheKey);
        }
      }

      // Se todos os valores são zero, retorna a imagem original
      if (brightness == 0.0 &&
          contrast == 0.0 &&
          saturation == 0.0 &&
          sharpness == 0.0) {
        return imagePath;
      }

      // Prepara os dados para o isolate
      final imageData = ImageProcessingData(
        imagePath: imagePath,
        brightness: brightness,
        contrast: contrast,
        saturation: saturation,
        sharpness: sharpness,
      );

      // Processa a imagem de forma assíncrona
      final processedPath = await _processImageAsync(imageData);

      // Adiciona ao cache
      _processedImageCache[cacheKey] = processedPath;

      // Limita o tamanho do cache
      if (_processedImageCache.length > 10) {
        _cleanupCache();
      }

      return processedPath;
    } catch (e) {
      debugPrint('Erro no processamento otimizado: $e');
      return imagePath; // Retorna a imagem original em caso de erro
    }
  }

  /// Gera uma chave única para o cache
  String _generateCacheKey(String imagePath, double brightness, double contrast,
      double saturation, double sharpness) {
    final fileName = imagePath.split('/').last;
    return '${fileName}_${brightness.toStringAsFixed(1)}_'
        '${contrast.toStringAsFixed(1)}_${saturation.toStringAsFixed(1)}_'
        '${sharpness.toStringAsFixed(1)}';
  }

  /// Limpa o cache removendo entradas antigas
  void _cleanupCache() {
    if (_processedImageCache.length <= 5) return;

    final keysToRemove = _processedImageCache.keys.take(5).toList();
    for (final key in keysToRemove) {
      final path = _processedImageCache.remove(key);
      if (path != null) {
        try {
          File(path).deleteSync();
        } catch (e) {
          debugPrint('Erro ao deletar cache: $e');
        }
      }
    }
  }

  /// Limpa todo o cache
  void clearCache() {
    for (final path in _processedImageCache.values) {
      try {
        File(path).deleteSync();
      } catch (e) {
        debugPrint('Erro ao deletar cache: $e');
      }
    }
    _processedImageCache.clear();
  }
}

/// Função que processa a imagem de forma assíncrona
Future<String> _processImageAsync(ImageProcessingData data) async {
  try {
    // Carrega a imagem
    final imageFile = File(data.imagePath);
    final imageBytes = await imageFile.readAsBytes();
    img.Image? image = img.decodeImage(imageBytes);

    if (image == null) {
      throw Exception('Não foi possível decodificar a imagem');
    }

    // Redimensiona a imagem se for muito grande para melhorar performance
    if (image.width > 2048 || image.height > 2048) {
      image = img.copyResize(image,
          width: image.width > image.height ? 2048 : null,
          height: image.height > image.width ? 2048 : null);
    }

    // Aplica os ajustes de forma otimizada
    if (data.brightness != 0.0) {
      image = _applyBrightnessOptimized(image, data.brightness);
    }

    if (data.contrast != 0.0) {
      image = _applyContrastOptimized(image, data.contrast);
    }

    if (data.saturation != 0.0) {
      image = _applySaturationOptimized(image, data.saturation);
    }

    if (data.sharpness != 0.0) {
      image = _applySharpnessOptimized(image, data.sharpness);
    }

    // Salva a imagem processada
    final processedPath = await _saveProcessedImageOptimized(image);
    return processedPath;
  } catch (e) {
    debugPrint('Erro no isolate de processamento: $e');
    return data.imagePath;
  }
}

/// Aplica brilho de forma otimizada
img.Image _applyBrightnessOptimized(img.Image image, double brightness) {
  final factor = (brightness / 100.0 * 255).round();

  for (int y = 0; y < image.height; y++) {
    for (int x = 0; x < image.width; x++) {
      final pixel = image.getPixel(x, y);
      final r = (pixel.r + factor).clamp(0, 255).toInt();
      final g = (pixel.g + factor).clamp(0, 255).toInt();
      final b = (pixel.b + factor).clamp(0, 255).toInt();

      image.setPixel(x, y, img.ColorRgba8(r, g, b, pixel.a.toInt()));
    }
  }

  return image;
}

/// Aplica contraste de forma otimizada
img.Image _applyContrastOptimized(img.Image image, double contrast) {
  final factor = (contrast + 100.0) / 100.0;

  for (int y = 0; y < image.height; y++) {
    for (int x = 0; x < image.width; x++) {
      final pixel = image.getPixel(x, y);
      final r = ((pixel.r - 128) * factor + 128).clamp(0, 255).toInt();
      final g = ((pixel.g - 128) * factor + 128).clamp(0, 255).toInt();
      final b = ((pixel.b - 128) * factor + 128).clamp(0, 255).toInt();

      image.setPixel(x, y, img.ColorRgba8(r, g, b, pixel.a.toInt()));
    }
  }

  return image;
}

/// Aplica saturação de forma otimizada
img.Image _applySaturationOptimized(img.Image image, double saturation) {
  final factor = (saturation + 100.0) / 100.0;

  for (int y = 0; y < image.height; y++) {
    for (int x = 0; x < image.width; x++) {
      final pixel = image.getPixel(x, y);

      // Converte para HSL para ajustar saturação
      final r = pixel.r / 255.0;
      final g = pixel.g / 255.0;
      final b = pixel.b / 255.0;

      final max = [r, g, b].reduce((a, b) => a > b ? a : b);
      final min = [r, g, b].reduce((a, b) => a < b ? a : b);
      final delta = max - min;

      if (delta == 0) continue; // Cor cinza, sem saturação

      final lightness = (max + min) / 2;
      final currentSaturation =
          lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);

      final newSaturation = (currentSaturation * factor).clamp(0.0, 1.0);

      // Converte de volta para RGB (simplificado)
      final saturationRatio = newSaturation / (currentSaturation + 0.001);

      final newR = ((r - lightness) * saturationRatio + lightness * 255)
          .clamp(0, 255)
          .toInt();
      final newG = ((g - lightness) * saturationRatio + lightness * 255)
          .clamp(0, 255)
          .toInt();
      final newB = ((b - lightness) * saturationRatio + lightness * 255)
          .clamp(0, 255)
          .toInt();

      image.setPixel(x, y, img.ColorRgba8(newR, newG, newB, pixel.a.toInt()));
    }
  }

  return image;
}

/// Aplica nitidez de forma otimizada (versão simplificada)
img.Image _applySharpnessOptimized(img.Image image, double sharpness) {
  if (sharpness == 0.0) return image;

  final factor = sharpness / 100.0;
  final result = img.Image.from(image);

  // Kernel de nitidez simplificado para melhor performance
  final kernel = [
    [0.0, -factor * 0.5, 0.0],
    [-factor * 0.5, 1 + 2 * factor, -factor * 0.5],
    [0.0, -factor * 0.5, 0.0],
  ];

  // Aplica apenas no centro da imagem para melhor performance
  final startX = image.width ~/ 4;
  final endX = (image.width * 3) ~/ 4;
  final startY = image.height ~/ 4;
  final endY = (image.height * 3) ~/ 4;

  for (int y = startY; y < endY - 1; y++) {
    for (int x = startX; x < endX - 1; x++) {
      double r = 0, g = 0, b = 0;

      for (int ky = 0; ky < 3; ky++) {
        for (int kx = 0; kx < 3; kx++) {
          final pixel = image.getPixel(x + kx - 1, y + ky - 1);
          final weight = kernel[ky][kx];

          r += pixel.r * weight;
          g += pixel.g * weight;
          b += pixel.b * weight;
        }
      }

      final originalPixel = image.getPixel(x, y);
      result.setPixel(
          x,
          y,
          img.ColorRgba8(
            r.clamp(0, 255).toInt(),
            g.clamp(0, 255).toInt(),
            b.clamp(0, 255).toInt(),
            originalPixel.a.toInt(),
          ));
    }
  }

  return result;
}

/// Salva a imagem processada de forma otimizada
Future<String> _saveProcessedImageOptimized(img.Image image) async {
  final directory = await getTemporaryDirectory();
  final timestamp = DateTime.now().millisecondsSinceEpoch;
  final filePath = '${directory.path}/optimized_$timestamp.jpg';

  // Usa qualidade otimizada para melhor performance
  final encodedImage = img.encodeJpg(image, quality: 85);
  final file = File(filePath);
  await file.writeAsBytes(encodedImage);

  return filePath;
}

/// Classe para dados de processamento de imagem
class ImageProcessingData {
  final String imagePath;
  final double brightness;
  final double contrast;
  final double saturation;
  final double sharpness;

  const ImageProcessingData({
    required this.imagePath,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.sharpness,
  });
}
