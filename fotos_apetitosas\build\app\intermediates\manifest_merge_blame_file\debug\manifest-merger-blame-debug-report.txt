1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fotosapetitosas.fotos_apetitosas"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:41:13-72
25-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:42:13-50
27-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.CAMERA" />
31-->[:camera_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-65
31-->[:camera_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-62
32    <uses-permission android:name="android.permission.RECORD_AUDIO" />
32-->[:camera_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-71
32-->[:camera_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-68
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.fotosapetitosas.fotos_apetitosas.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.fotosapetitosas.fotos_apetitosas.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:icon="@mipmap/ic_launcher"
46        android:label="fotos_apetitosas" >
47        <activity
48            android:name="com.fotosapetitosas.fotos_apetitosas.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <provider
81-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
82            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
82-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
83            android:authorities="com.fotosapetitosas.fotos_apetitosas.flutter.image_provider"
83-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
84            android:exported="false"
84-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
85            android:grantUriPermissions="true" >
85-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
86            <meta-data
86-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
87                android:name="android.support.FILE_PROVIDER_PATHS"
87-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
88                android:resource="@xml/flutter_image_picker_file_paths" />
88-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
89        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
90        <service
90-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
91            android:name="com.google.android.gms.metadata.ModuleDependencies"
91-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
92            android:enabled="false"
92-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
93            android:exported="false" >
93-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
94            <intent-filter>
94-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
95                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
95-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
95-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
96            </intent-filter>
97
98            <meta-data
98-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
99                android:name="photopicker_activity:0:required"
99-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
100                android:value="" />
100-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
101        </service>
102
103        <uses-library
103-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
104            android:name="androidx.window.extensions"
104-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
105            android:required="false" />
105-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
106        <uses-library
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
107            android:name="androidx.window.sidecar"
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
108            android:required="false" />
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
109
110        <provider
110-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
111            android:name="androidx.startup.InitializationProvider"
111-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
112            android:authorities="com.fotosapetitosas.fotos_apetitosas.androidx-startup"
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
113            android:exported="false" >
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
114            <meta-data
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
115                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
116                android:value="androidx.startup" />
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
117            <meta-data
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
119                android:value="androidx.startup" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
120        </provider>
121
122        <receiver
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
123            android:name="androidx.profileinstaller.ProfileInstallReceiver"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
124            android:directBootAware="false"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
125            android:enabled="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
126            android:exported="true"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
127            android:permission="android.permission.DUMP" >
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
129                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
130            </intent-filter>
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
132                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
135                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
138                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
139            </intent-filter>
140        </receiver>
141    </application>
142
143</manifest>
