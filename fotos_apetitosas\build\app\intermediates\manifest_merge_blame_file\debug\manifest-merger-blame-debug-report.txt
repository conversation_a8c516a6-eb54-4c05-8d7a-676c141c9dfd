1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fotosapetitosas.fotos_apetitosas"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- <PERSON><PERSON>s<PERSON>es para câmera e armazenamento -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:4:5-65
17-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:4:22-62
18    <uses-permission
18-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:5:5-6:51
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:5:22-78
20        android:maxSdkVersion="32" />
20-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:6:22-48
21    <uses-permission
21-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:7:5-8:51
22        android:name="android.permission.READ_EXTERNAL_STORAGE"
22-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:7:22-77
23        android:maxSdkVersion="32" />
23-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:8:22-48
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:9:5-76
24-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:9:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:10:5-75
25-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:10:22-72
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:11:5-71
26-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:11:22-68
27    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Recursos necessários -->
27-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:12:5-66
27-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:12:22-63
28    <uses-feature
28-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:15:5-84
29        android:name="android.hardware.camera"
29-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:15:19-57
30        android:required="true" />
30-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:15:58-81
31    <uses-feature
31-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:16:5-95
32        android:name="android.hardware.camera.autofocus"
32-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:16:19-67
33        android:required="false" />
33-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:16:68-92
34    <uses-feature
34-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:17:5-91
35        android:name="android.hardware.camera.flash"
35-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:17:19-63
36        android:required="false" />
36-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:17:64-88
37    <!--
38 Required to query activities that can process text, see:
39         https://developer.android.com/training/package-visibility and
40         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
41
42         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
43    -->
44    <queries>
44-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:56:5-61:15
45        <intent>
45-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:57:9-60:18
46            <action android:name="android.intent.action.PROCESS_TEXT" />
46-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:58:13-72
46-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:58:21-70
47
48            <data android:mimeType="text/plain" />
48-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:59:13-50
48-->F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\AndroidManifest.xml:59:19-48
49        </intent>
50    </queries>
51
52    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
52-->[:flutter_local_notifications] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
52-->[:flutter_local_notifications] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-74
53
54    <permission
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
55        android:name="com.fotosapetitosas.fotos_apetitosas.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.fotosapetitosas.fotos_apetitosas.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
59
60    <application
61        android:name="android.app.Application"
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
63        android:debuggable="true"
64        android:extractNativeLibs="true"
65        android:icon="@mipmap/ic_launcher"
66        android:label="fotos_apetitosas" >
67        <activity
68            android:name="com.fotosapetitosas.fotos_apetitosas.MainActivity"
69            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
70            android:exported="true"
71            android:hardwareAccelerated="true"
72            android:launchMode="singleTop"
73            android:taskAffinity=""
74            android:theme="@style/LaunchTheme"
75            android:windowSoftInputMode="adjustResize" >
76
77            <!--
78                 Specifies an Android theme to apply to this Activity as soon as
79                 the Android process has started. This theme is visible to the user
80                 while the Flutter UI initializes. After that, this theme continues
81                 to determine the Window background behind the Flutter UI.
82            -->
83            <meta-data
84                android:name="io.flutter.embedding.android.NormalTheme"
85                android:resource="@style/NormalTheme" />
86
87            <intent-filter>
88                <action android:name="android.intent.action.MAIN" />
89
90                <category android:name="android.intent.category.LAUNCHER" />
91            </intent-filter>
92        </activity>
93        <!--
94             Don't delete the meta-data below.
95             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
96        -->
97        <meta-data
98            android:name="flutterEmbedding"
99            android:value="2" />
100
101        <provider
101-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
102            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
102-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
103            android:authorities="com.fotosapetitosas.fotos_apetitosas.flutter.image_provider"
103-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
104            android:exported="false"
104-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
105            android:grantUriPermissions="true" >
105-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
106            <meta-data
106-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
107                android:name="android.support.FILE_PROVIDER_PATHS"
107-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
108                android:resource="@xml/flutter_image_picker_file_paths" />
108-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
109        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
110        <service
110-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
111            android:name="com.google.android.gms.metadata.ModuleDependencies"
111-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
112            android:enabled="false"
112-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
113            android:exported="false" >
113-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
114            <intent-filter>
114-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
115                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
115-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
115-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
116            </intent-filter>
117
118            <meta-data
118-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
119                android:name="photopicker_activity:0:required"
119-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
120                android:value="" />
120-->[:image_picker_android] F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
121        </service>
122
123        <uses-library
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
124            android:name="androidx.window.extensions"
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
125            android:required="false" />
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
126        <uses-library
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
127            android:name="androidx.window.sidecar"
127-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
128            android:required="false" />
128-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
129
130        <provider
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
131            android:name="androidx.startup.InitializationProvider"
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
132            android:authorities="com.fotosapetitosas.fotos_apetitosas.androidx-startup"
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
133            android:exported="false" >
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
134            <meta-data
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
135-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
136                android:value="androidx.startup" />
136-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
137            <meta-data
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
138                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
139                android:value="androidx.startup" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
140        </provider>
141
142        <receiver
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
143            android:name="androidx.profileinstaller.ProfileInstallReceiver"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
144            android:directBootAware="false"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
145            android:enabled="true"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
146            android:exported="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
147            android:permission="android.permission.DUMP" >
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
149                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
152                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
155                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
158                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
159            </intent-filter>
160        </receiver>
161    </application>
162
163</manifest>
