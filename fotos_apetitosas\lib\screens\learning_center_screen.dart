import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';

/// Centro de aprendizado com dicas e tutoriais de fotografia
class LearningCenterScreen extends StatefulWidget {
  const LearningCenterScreen({super.key});

  @override
  State<LearningCenterScreen> createState() => _LearningCenterScreenState();
}

class _LearningCenterScreenState extends State<LearningCenterScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Centro de Aprendizado'),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Fundamentos'),
            Tab(text: 'Por Tipo'),
            Tab(text: 'Erros Comuns'),
            Tab(text: 'Inspiração'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFundamentalsTab(),
          _buildByTypeTab(),
          _buildCommonErrorsTab(),
          _buildInspirationTab(),
        ],
      ),
    );
  }

  Widget _buildFundamentalsTab() {
    final fundamentals = [
      LearningCard(
        title: 'Regra dos Terços',
        description: 'Posicione elementos importantes nos pontos de intersecção das linhas imaginárias que dividem a imagem em terços.',
        icon: Icons.grid_on,
        color: AppColors.primary,
      ),
      LearningCard(
        title: 'Iluminação Natural',
        description: 'Use luz natural sempre que possível. A melhor luz vem de janelas com luz difusa.',
        icon: Icons.wb_sunny,
        color: AppColors.secondary,
      ),
      LearningCard(
        title: 'Ângulos Ideais',
        description: 'Para doces altos, fotografe ligeiramente de cima. Para arranjos, use ângulos de 45°.',
        icon: Icons.camera_alt,
        color: AppColors.accent,
      ),
      LearningCard(
        title: 'Fundo Limpo',
        description: 'Mantenha o fundo simples e sem distrações para destacar seu doce.',
        icon: Icons.crop_free,
        color: AppColors.info,
      ),
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: fundamentals.length,
      itemBuilder: (context, index) {
        return _buildLearningCard(fundamentals[index])
            .animate(delay: (index * 100).ms)
            .slideX(begin: 0.3, duration: 400.ms, curve: Curves.easeOut)
            .fadeIn(duration: 400.ms);
      },
    );
  }

  Widget _buildByTypeTab() {
    final types = [
      TypeCard(
        title: 'Cupcakes',
        emoji: '🧁',
        tips: [
          'Fotografe em grupos ímpares (3, 5, 7)',
          'Destaque o frosting e decorações',
          'Use luz lateral para criar textura',
        ],
        color: AppColors.primary,
      ),
      TypeCard(
        title: 'Bolos',
        emoji: '🎂',
        tips: [
          'Ângulo de 45° realça camadas e decoração',
          'Mostre a altura e os detalhes',
          'Capture fatias para mostrar o interior',
        ],
        color: AppColors.secondary,
      ),
      TypeCard(
        title: 'Cookies',
        emoji: '🍪',
        tips: [
          'Luz lateral destaca textura e relevo',
          'Agrupe de forma orgânica',
          'Mostre diferentes formatos juntos',
        ],
        color: AppColors.accent,
      ),
      TypeCard(
        title: 'Docinhos',
        emoji: '🍭',
        tips: [
          'Crie padrões e use cores contrastantes',
          'Fotografe de cima para mostrar variedade',
          'Use props que complementem as cores',
        ],
        color: AppColors.info,
      ),
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: types.length,
      itemBuilder: (context, index) {
        return _buildTypeCard(types[index])
            .animate(delay: (index * 100).ms)
            .slideX(begin: -0.3, duration: 400.ms, curve: Curves.easeOut)
            .fadeIn(duration: 400.ms);
      },
    );
  }

  Widget _buildCommonErrorsTab() {
    final errors = [
      ErrorCard(
        error: 'Foto muito escura',
        solution: 'Use luz natural da janela',
        errorIcon: Icons.brightness_low,
        solutionIcon: Icons.wb_sunny,
      ),
      ErrorCard(
        error: 'Doce centralizado',
        solution: 'Use a regra dos terços',
        errorIcon: Icons.center_focus_weak,
        solutionIcon: Icons.grid_on,
      ),
      ErrorCard(
        error: 'Fundo bagunçado',
        solution: 'Simplifique o background',
        errorIcon: Icons.blur_on,
        solutionIcon: Icons.crop_free,
      ),
      ErrorCard(
        error: 'Flash direto',
        solution: 'Use luz difusa ou natural',
        errorIcon: Icons.flash_on,
        solutionIcon: Icons.wb_cloudy,
      ),
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: errors.length,
      itemBuilder: (context, index) {
        return _buildErrorCard(errors[index])
            .animate(delay: (index * 100).ms)
            .slideY(begin: 0.3, duration: 400.ms, curve: Curves.easeOut)
            .fadeIn(duration: 400.ms);
      },
    );
  }

  Widget _buildInspirationTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: AppSpacing.md),
          Text(
            'Galeria de Inspiração',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppSpacing.sm),
          Text(
            'Em breve: exemplos de fotos\nincríveis feitas por outros usuários!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLearningCard(LearningCard card) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: card.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppRadius.md),
              ),
              child: Icon(
                card.icon,
                color: card.color,
                size: 30,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    card.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeCard(TypeCard card) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  card.emoji,
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  card.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: card.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),
            ...card.tips.map((tip) => Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: card.color,
                        size: 16,
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: Text(
                          tip,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorCard(ErrorCard card) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            // Erro
            Expanded(
              child: Column(
                children: [
                  Icon(
                    card.errorIcon,
                    color: AppColors.error,
                    size: 32,
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    card.error,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            // Seta
            const Icon(
              Icons.arrow_forward,
              color: AppColors.onSurfaceVariant,
            ),
            
            // Solução
            Expanded(
              child: Column(
                children: [
                  Icon(
                    card.solutionIcon,
                    color: AppColors.success,
                    size: 32,
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    card.solution,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.success,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Modelos de dados
class LearningCard {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const LearningCard({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class TypeCard {
  final String title;
  final String emoji;
  final List<String> tips;
  final Color color;

  const TypeCard({
    required this.title,
    required this.emoji,
    required this.tips,
    required this.color,
  });
}

class ErrorCard {
  final String error;
  final String solution;
  final IconData errorIcon;
  final IconData solutionIcon;

  const ErrorCard({
    required this.error,
    required this.solution,
    required this.errorIcon,
    required this.solutionIcon,
  });
}
