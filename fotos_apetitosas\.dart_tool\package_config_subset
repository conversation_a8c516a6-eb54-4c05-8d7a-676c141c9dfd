fotos_apetitosas
3.5
file:///F:/Projetos/02%20-%20Dev/guiadefoto/fotos_apetitosas/
file:///F:/Projetos/02%20-%20Dev/guiadefoto/fotos_apetitosas/lib/
archive
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/archive-4.0.7/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/archive-4.0.7/lib/
async
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.11.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.11.0/lib/
boolean_selector
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.1/lib/
camera
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera-0.10.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera-0.10.6/lib/
camera_android
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_android-0.10.10/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_android-0.10.10/lib/
camera_avfoundation
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_avfoundation-0.9.19/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_avfoundation-0.9.19/lib/
camera_platform_interface
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_platform_interface-2.10.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_platform_interface-2.10.0/lib/
camera_web
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_web-0.3.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_web-0.3.5/lib/
characters
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.3.0/lib/
clock
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.1/lib/
collection
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.18.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.18.0/lib/
cross_file
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cross_file-0.3.4+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/crypto-3.0.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ffi-2.1.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file-7.0.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_macos-0.9.4+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/
file_selector_platform_interface
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
flutter_animate
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_animate-4.5.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_animate-4.5.2/lib/
flutter_lints
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-4.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-4.0.0/lib/
flutter_plugin_android_lifecycle
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/lib/
flutter_shaders
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_shaders-0.1.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_shaders-0.1.3/lib/
go_router
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/go_router-12.1.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/go_router-12.1.3/lib/
http
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http-0.13.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http-0.13.6/lib/
http_parser
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http_parser-4.0.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http_parser-4.0.2/lib/
image
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image-4.5.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker-1.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_android-0.8.12+21/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_android-0.8.12+21/lib/
image_picker_for_web
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
leak_tracker
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.5/lib/
leak_tracker_flutter_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/lib/
leak_tracker_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-4.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-4.0.0/lib/
logging
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/logging-1.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.16+1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.15.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.15.0/lib/
mime
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/mime-2.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/mime-2.0.0/lib/
nested
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/lib/
path
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.0/lib/
path_provider
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider-2.1.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_android-2.2.15/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_android-2.2.15/lib/
path_provider_foundation
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_linux-2.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_windows-2.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler-11.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_android-12.1.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/petitparser-6.0.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/petitparser-6.0.2/lib/
platform
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/platform-3.1.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
posix
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/posix-6.0.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/lib/
shared_preferences
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences-2.5.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_android-2.4.7/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_android-2.4.7/lib/
shared_preferences_foundation
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
source_span
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.0/lib/
stack_trace
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.11.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.11.1/lib/
stream_channel
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.2/lib/
stream_transform
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_transform-2.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
2.18
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.2.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.2.0/lib/
term_glyph
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.2/lib/
typed_data
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/typed_data-1.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/typed_data-1.4.0/lib/
vector_math
2.14
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-14.2.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-14.2.5/lib/
web
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/web-1.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/web-1.1.1/lib/
xdg_directories
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xdg_directories-1.1.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xml-6.5.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xml-6.5.0/lib/
sky_engine
3.2
file:///f:/Projetos/fvm/versions/stable/bin/cache/pkg/sky_engine/
file:///f:/Projetos/fvm/versions/stable/bin/cache/pkg/sky_engine/lib/
flutter
3.3
file:///f:/Projetos/fvm/versions/stable/packages/flutter/
file:///f:/Projetos/fvm/versions/stable/packages/flutter/lib/
flutter_test
3.3
file:///f:/Projetos/fvm/versions/stable/packages/flutter_test/
file:///f:/Projetos/fvm/versions/stable/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///f:/Projetos/fvm/versions/stable/packages/flutter_web_plugins/
file:///f:/Projetos/fvm/versions/stable/packages/flutter_web_plugins/lib/
2
