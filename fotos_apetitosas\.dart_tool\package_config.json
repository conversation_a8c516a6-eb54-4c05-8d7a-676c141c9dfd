{"configVersion": 2, "packages": [{"name": "archive", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "camera", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera-0.10.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "camera_android", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_android-0.10.10", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "camera_avfoundation", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_avfoundation-0.9.19", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "camera_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "camera_web", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/camera_web-0.3.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "characters", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cross_file", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_macos-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter", "rootUri": "file:///f:/Projetos/fvm/versions/stable/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_animate", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_animate-4.5.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_lints", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_shaders", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_shaders-0.1.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_test", "rootUri": "file:///f:/Projetos/fvm/versions/stable/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///f:/Projetos/fvm/versions/stable/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "go_router", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/go_router-12.1.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "http", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http-0.13.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http_parser", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_android-0.8.12+21", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "image_picker_for_web", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "leak_tracker", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "logging", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_android-2.2.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "path_provider_foundation", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "posix", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/posix-6.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared_preferences", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_android-2.4.7", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_foundation", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///f:/Projetos/fvm/versions/stable/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "typed_data", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fotos_apetitosas", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.5"}], "generated": "2025-06-21T21:09:56.004626Z", "generator": "pub", "generatorVersion": "3.5.3", "flutterRoot": "file:///f:/Projetos/fvm/versions/stable", "flutterVersion": "3.24.3", "pubCache": "file:///F:/Projetos/PUB_CACHE"}