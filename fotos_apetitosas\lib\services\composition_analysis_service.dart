import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

/// Serviço para análise inteligente de composição fotográfica
class CompositionAnalysisService {
  static final CompositionAnalysisService _instance =
      CompositionAnalysisService._internal();
  factory CompositionAnalysisService() => _instance;
  CompositionAnalysisService._internal();

  /// Analisa a composição de uma imagem e retorna sugestões
  Future<CompositionAnalysis> analyzeComposition(String imagePath) async {
    try {
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        return CompositionAnalysis.unknown();
      }

      // Análises diferentes
      final ruleOfThirds = _analyzeRuleOfThirds(image);
      final centerBalance = _analyzeCenterBalance(image);
      final edgeDetection = _analyzeEdges(image);
      final colorDistribution = _analyzeColorDistribution(image);

      // Calcula pontuação geral
      final overallScore = _calculateOverallScore([
        ruleOfThirds,
        centerBalance,
        edgeDetection,
        colorDistribution,
      ]);

      return CompositionAnalysis(
        overallScore: overallScore,
        ruleOfThirdsScore: ruleOfThirds,
        centerBalanceScore: centerBalance,
        edgeDetectionScore: edgeDetection,
        colorDistributionScore: colorDistribution,
        suggestions:
            _generateSuggestions(overallScore, ruleOfThirds, centerBalance),
        quality: _getQualityFromScore(overallScore),
      );
    } catch (e) {
      debugPrint('Erro ao analisar composição: $e');
      return CompositionAnalysis.unknown();
    }
  }

  /// Analisa se a imagem segue a regra dos terços
  double _analyzeRuleOfThirds(img.Image image) {
    final width = image.width;
    final height = image.height;

    // Pontos de interesse da regra dos terços
    final thirdX1 = width ~/ 3;
    final thirdX2 = (width * 2) ~/ 3;
    final thirdY1 = height ~/ 3;
    final thirdY2 = (height * 2) ~/ 3;

    // Analisa a densidade de pixels interessantes nos pontos de intersecção
    double score = 0.0;
    final regions = [
      Point(thirdX1, thirdY1),
      Point(thirdX2, thirdY1),
      Point(thirdX1, thirdY2),
      Point(thirdX2, thirdY2),
    ];

    for (final point in regions) {
      final regionScore = _analyzeRegionInterest(image, point.x, point.y, 50);
      score += regionScore;
    }

    return (score / regions.length).clamp(0.0, 1.0);
  }

  /// Analisa o equilíbrio central da imagem
  double _analyzeCenterBalance(img.Image image) {
    final centerX = image.width ~/ 2;
    final centerY = image.height ~/ 2;

    // Analisa a distribuição de peso visual ao redor do centro
    final centerWeight = _analyzeRegionInterest(image, centerX, centerY, 100);

    // Penaliza composições muito centralizadas (exceto para casos específicos)
    if (centerWeight > 0.8) {
      return 0.3; // Muito centralizado
    } else if (centerWeight > 0.5) {
      return 0.6; // Moderadamente centralizado
    } else {
      return 0.9; // Bem distribuído
    }
  }

  /// Analisa a detecção de bordas (nitidez e definição)
  double _analyzeEdges(img.Image image) {
    // Aplica filtro Sobel para detecção de bordas
    final edges = _applySobelFilter(image);

    // Conta pixels com bordas significativas
    int edgePixels = 0;
    int totalPixels = 0;

    for (int y = 0; y < edges.height; y += 5) {
      for (int x = 0; x < edges.width; x += 5) {
        final pixel = edges.getPixel(x, y);
        final intensity = 0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b;

        if (intensity > 50) {
          // Threshold para bordas
          edgePixels++;
        }
        totalPixels++;
      }
    }

    final edgeRatio = edgePixels / totalPixels;
    return edgeRatio.clamp(0.0, 1.0);
  }

  /// Analisa a distribuição de cores na imagem
  double _analyzeColorDistribution(img.Image image) {
    final colorMap = <int, int>{};
    int totalPixels = 0;

    // Amostra cores (a cada 10 pixels para performance)
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r ~/ 32; // Reduz precisão para agrupamento
        final g = pixel.g ~/ 32;
        final b = pixel.b ~/ 32;
        final colorKey = (r << 16) | (g << 8) | b;

        colorMap[colorKey] = (colorMap[colorKey] ?? 0) + 1;
        totalPixels++;
      }
    }

    // Calcula diversidade de cores (entropia)
    double entropy = 0.0;
    for (final count in colorMap.values) {
      final probability = count / totalPixels;
      if (probability > 0) {
        entropy -= probability * log(probability) / ln2;
      }
    }

    // Normaliza a entropia (valor típico entre 0-8)
    return (entropy / 8.0).clamp(0.0, 1.0);
  }

  /// Analisa o interesse visual de uma região específica
  double _analyzeRegionInterest(
      img.Image image, int centerX, int centerY, int radius) {
    double totalInterest = 0.0;
    int pixelCount = 0;

    final startX = (centerX - radius).clamp(0, image.width - 1);
    final endX = (centerX + radius).clamp(0, image.width - 1);
    final startY = (centerY - radius).clamp(0, image.height - 1);
    final endY = (centerY + radius).clamp(0, image.height - 1);

    for (int y = startY; y < endY; y += 3) {
      for (int x = startX; x < endX; x += 3) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r.toInt();
        final g = pixel.g.toInt();
        final b = pixel.b.toInt();

        // Calcula interesse baseado em contraste e saturação
        final luminance = 0.299 * r + 0.587 * g + 0.114 * b;
        final saturation = _calculateSaturation(r, g, b);

        final interest = (saturation + (luminance / 255.0)) / 2.0;
        totalInterest += interest;
        pixelCount++;
      }
    }

    return pixelCount > 0 ? totalInterest / pixelCount : 0.0;
  }

  /// Aplica filtro Sobel para detecção de bordas
  img.Image _applySobelFilter(img.Image image) {
    final result = img.Image.from(image);

    // Kernels Sobel
    final sobelX = [
      [-1, 0, 1],
      [-2, 0, 2],
      [-1, 0, 1],
    ];

    final sobelY = [
      [-1, -2, -1],
      [0, 0, 0],
      [1, 2, 1],
    ];

    for (int y = 1; y < image.height - 1; y++) {
      for (int x = 1; x < image.width - 1; x++) {
        double gx = 0, gy = 0;

        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final pixel = image.getPixel(x + kx - 1, y + ky - 1);
            final intensity = img.getLuminance(pixel);

            gx += intensity * sobelX[ky][kx];
            gy += intensity * sobelY[ky][kx];
          }
        }

        final magnitude = sqrt(gx * gx + gy * gy).clamp(0, 255).toInt();
        result.setPixel(
            x, y, img.ColorRgba8(magnitude, magnitude, magnitude, 255));
      }
    }

    return result;
  }

  /// Calcula a saturação de uma cor RGB
  double _calculateSaturation(int r, int g, int b) {
    final max = [r, g, b].reduce((a, b) => a > b ? a : b);
    final min = [r, g, b].reduce((a, b) => a < b ? a : b);

    if (max == 0) return 0.0;
    return (max - min) / max;
  }

  /// Calcula pontuação geral baseada em múltiplos fatores
  double _calculateOverallScore(List<double> scores) {
    if (scores.isEmpty) return 0.0;

    // Média ponderada
    final weights = [0.3, 0.2, 0.3, 0.2]; // Pesos para cada análise
    double weightedSum = 0.0;
    double totalWeight = 0.0;

    for (int i = 0; i < scores.length && i < weights.length; i++) {
      weightedSum += scores[i] * weights[i];
      totalWeight += weights[i];
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0.0;
  }

  /// Gera sugestões baseadas na análise
  List<String> _generateSuggestions(
      double overall, double ruleOfThirds, double centerBalance) {
    final suggestions = <String>[];

    if (ruleOfThirds < 0.4) {
      suggestions
          .add('Tente posicionar o doce nos pontos de intersecção da grade');
    }

    if (centerBalance < 0.4) {
      suggestions.add('Evite centralizar demais - use a regra dos terços');
    }

    if (overall < 0.5) {
      suggestions.add('Experimente diferentes ângulos e posicionamentos');
    } else if (overall > 0.8) {
      suggestions.add('Excelente composição! Continue assim! 🎉');
    }

    if (suggestions.isEmpty) {
      suggestions
          .add('Boa composição! Pequenos ajustes podem melhorar ainda mais');
    }

    return suggestions;
  }

  /// Determina a qualidade baseada na pontuação
  CompositionQuality _getQualityFromScore(double score) {
    if (score >= 0.8) return CompositionQuality.excellent;
    if (score >= 0.6) return CompositionQuality.good;
    if (score >= 0.4) return CompositionQuality.fair;
    return CompositionQuality.poor;
  }
}

/// Resultado da análise de composição
class CompositionAnalysis {
  final double overallScore;
  final double ruleOfThirdsScore;
  final double centerBalanceScore;
  final double edgeDetectionScore;
  final double colorDistributionScore;
  final List<String> suggestions;
  final CompositionQuality quality;

  const CompositionAnalysis({
    required this.overallScore,
    required this.ruleOfThirdsScore,
    required this.centerBalanceScore,
    required this.edgeDetectionScore,
    required this.colorDistributionScore,
    required this.suggestions,
    required this.quality,
  });

  factory CompositionAnalysis.unknown() {
    return const CompositionAnalysis(
      overallScore: 0.0,
      ruleOfThirdsScore: 0.0,
      centerBalanceScore: 0.0,
      edgeDetectionScore: 0.0,
      colorDistributionScore: 0.0,
      suggestions: ['Não foi possível analisar a composição'],
      quality: CompositionQuality.unknown,
    );
  }

  /// Retorna a pontuação como porcentagem
  int get scorePercentage => (overallScore * 100).round();

  /// Retorna a cor baseada na qualidade
  Color get qualityColor {
    switch (quality) {
      case CompositionQuality.excellent:
        return Colors.green;
      case CompositionQuality.good:
        return Colors.lightGreen;
      case CompositionQuality.fair:
        return Colors.orange;
      case CompositionQuality.poor:
        return Colors.red;
      case CompositionQuality.unknown:
        return Colors.grey;
    }
  }

  /// Retorna o ícone baseado na qualidade
  IconData get qualityIcon {
    switch (quality) {
      case CompositionQuality.excellent:
        return Icons.star;
      case CompositionQuality.good:
        return Icons.thumb_up;
      case CompositionQuality.fair:
        return Icons.thumbs_up_down;
      case CompositionQuality.poor:
        return Icons.thumb_down;
      case CompositionQuality.unknown:
        return Icons.help_outline;
    }
  }
}

/// Qualidade da composição
enum CompositionQuality {
  excellent,
  good,
  fair,
  poor,
  unknown,
}

/// Extensão para obter descrições das qualidades
extension CompositionQualityExtension on CompositionQuality {
  String get description {
    switch (this) {
      case CompositionQuality.excellent:
        return 'Excelente';
      case CompositionQuality.good:
        return 'Boa';
      case CompositionQuality.fair:
        return 'Regular';
      case CompositionQuality.poor:
        return 'Precisa melhorar';
      case CompositionQuality.unknown:
        return 'Desconhecida';
    }
  }
}
