{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\camera_avfoundation-0.9.19\\\\", "native_build": true, "dependencies": []}, {"name": "gallery_saver", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\gallery_saver-2.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.12+2\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "camera_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\camera_android-0.10.10\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_plugin_android_lifecycle", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.26\\\\", "native_build": true, "dependencies": []}, {"name": "gallery_saver", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\gallery_saver-2.3.2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.12+21\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.15\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.7\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "file_selector_macos", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "path_provider_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "file_selector_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "path_provider_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"]}], "windows": [{"name": "file_selector_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+4\\\\", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "path_provider_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"]}], "web": [{"name": "camera_web", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\camera_web-0.3.5\\\\", "dependencies": []}, {"name": "image_picker_for_web", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\image_picker_for_web-3.0.6\\\\", "dependencies": []}, {"name": "permission_handler_html", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": []}, {"name": "shared_preferences_web", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": []}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "gallery_saver", "dependencies": ["path_provider"]}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}], "date_created": "2025-06-21 17:57:59.577861", "version": "3.24.3", "swift_package_manager_enabled": false}