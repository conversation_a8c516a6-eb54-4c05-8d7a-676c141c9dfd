import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';  // Temporariamente removido
import 'package:go_router/go_router.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';

/// Tela de exportação e salvamento da foto editada
class ExportScreen extends StatefulWidget {
  final String? imagePath;

  const ExportScreen({
    super.key,
    required this.imagePath,
  });

  @override
  State<ExportScreen> createState() => _ExportScreenState();
}

class _ExportScreenState extends State<ExportScreen> {
  bool _isSaving = false;
  bool _isSaved = false;

  @override
  Widget build(BuildContext context) {
    if (widget.imagePath == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Exportar')),
        body: const Center(
          child: Text('Nenhuma imagem para exportar'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Sua Foto Está Pronta!'),
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            children: [
              // Preview final da imagem
              Expanded(
                flex: 3,
                child: _buildFinalPreview(),
              ),

              const SizedBox(height: AppSpacing.lg),

              // Informações sobre a qualidade
              _buildQualityInfo(),

              const SizedBox(height: AppSpacing.lg),

              // Status de salvamento
              if (_isSaved) _buildSuccessMessage(),

              const Spacer(),

              // Botões de ação
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinalPreview() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.lg),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.lg),
        child: Image.file(
          File(widget.imagePath!),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildQualityInfo() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(
          color: AppColors.surfaceVariant,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.high_quality,
            color: AppColors.success,
            size: 24,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Alta Qualidade',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                ),
                Text(
                  'Sua foto será salva com 90% de qualidade JPEG',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.md),
        border: Border.all(
          color: AppColors.success,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 24,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Foto Salva com Sucesso!',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                ),
                Text(
                  'Sua foto foi salva na galeria do dispositivo',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    )
        .animate()
        .scale(duration: 400.ms, curve: Curves.elasticOut)
        .fadeIn(duration: 300.ms);
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Botão principal - Salvar na Galeria
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isSaving || _isSaved ? null : _saveToGallery,
            icon: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Icon(_isSaved ? Icons.check : Icons.save_alt),
            label: Text(_isSaving
                ? 'Salvando...'
                : _isSaved
                    ? 'Salvo na Galeria'
                    : 'Salvar na Galeria'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isSaved ? AppColors.success : AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
            ),
          ),
        ),

        const SizedBox(height: AppSpacing.md),

        // Botões secundários
        Row(
          children: [
            // Tirar Nova Foto
            Expanded(
              child: TextButton.icon(
                onPressed: () => context.go('/'),
                icon: const Icon(Icons.camera_alt_outlined),
                label: const Text('Nova Foto'),
              ),
            ),

            const SizedBox(width: AppSpacing.md),

            // Editar Novamente
            Expanded(
              child: TextButton.icon(
                onPressed: () => context.pop(),
                icon: const Icon(Icons.edit_outlined),
                label: const Text('Editar'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _saveToGallery() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Simulação de salvamento para demonstração
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _isSaved = true;
      });

      // Mostrar feedback de sucesso
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Foto processada com sucesso! (Demo)'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.sm),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Erro ao salvar na galeria: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Erro ao salvar: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.sm),
            ),
            action: SnackBarAction(
              label: 'Tentar Novamente',
              textColor: Colors.white,
              onPressed: _saveToGallery,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
