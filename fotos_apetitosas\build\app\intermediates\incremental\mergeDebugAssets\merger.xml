<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":vibration" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\vibration\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\sqflite_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\shared_preferences_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\permission_handler_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\path_provider_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\image_picker_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\flutter_local_notifications\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\device_info_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":camera_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\camera_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\Projetos\02 - Dev\guiadefoto\fotos_apetitosas\build\app\intermediates\shader_assets\debug\out"/></dataSet></merger>