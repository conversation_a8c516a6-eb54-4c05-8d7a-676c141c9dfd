# Especificação Técnica - FotosApetitosas MVP
## App Flutter para Fotografia de Doces e Confeitaria

### 📋 Visão Geral
**Objetivo:** <PERSON><PERSON><PERSON> um MVP que prove o valor da ideia para confeiteiros que desejam tirar fotos melhores de seus produtos.

**Plataforma:** Flutter (iOS e Android)
**Público-alvo:** Confeiteiros, doceiros e profissionais de confeitaria
**Proposta de valor:** Simplificar o processo de captura e edição de fotos de doces com ferramentas otimizadas

---

## 🎯 Funcionalidades do MVP

### 1. Tela Principal (Câmera Integrada com Guias Inteligentes)
**Arquivo:** `lib/screens/camera_screen.dart`

#### Sistema de Guias Fotográficas Inteligentes:

##### **Guias de Composição Visuais:**
- **Regra dos Terços:** Grade 3x3 com pontos de interesse destacados
- **Grade Centralizada:** Para doces simétricos (bolos redondos, cupcakes)
- **G<PERSON><PERSON>:** Para arranjos dinâmicos de múltiplos doces
- **Moldura Dourada:** Proporção áurea para composições elegantes

##### **Assistente de Enquadramento em Tempo Real:**
- **Detecção de Objeto:** IA básica para identificar doces na tela
- **Sugestões de Posicionamento:**
  - "Mova o doce para o ponto de interesse" (com seta animada)
  - "Aproxime-se mais para destacar os detalhes"
  - "Afaste-se para mostrar o contexto"
- **Indicador de Qualidade:** Barra de progresso mostrando qualidade da composição (0-100%)

##### **Guias de Iluminação:**
- **Detector de Luz:** Análise automática das condições de iluminação
- **Sugestões Contextuais:**
  - 🌞 "Ótima luz natural! Perfeito para cores vibrantes"
  - ⚠️ "Luz muito forte - tente um ângulo diferente"
  - 💡 "Pouca luz - ative o flash ou mude de posição"
  - 🌅 "Luz lateral detectada - ideal para texturas"

##### **Modo Tutorial Interativo:**
- **Primeira Foto:** Passo-a-passo guiado
  1. "Posicione seu doce no centro da tela"
  2. "Agora mova para o ponto de interesse (canto da grade)"
  3. "Perfeito! Veja como a foto ficou mais interessante"
- **Dicas Contextuais:** Baseadas no tipo de doce detectado
  - Bolos: "Para bolos altos, fotografe ligeiramente de cima"
  - Cupcakes: "Agrupe 3-4 cupcakes para composição dinâmica"
  - Docinhos: "Use a regra dos terços para múltiplos docinhos"

#### Funcionalidades Técnicas:
- **Captura de Imagem:** Acesso direto à câmera do dispositivo
- **Seleção de Galeria:** Importar fotos existentes da galeria
- **Controles de Câmera:**
  - Botão de disparo principal
  - Troca entre câmera frontal/traseira
  - Flash inteligente (auto-sugestão baseada na luz ambiente)
  - Foco automático com toque para focar

#### Componentes Técnicos:
- `camera` package para acesso à câmera
- `image_picker` package para seleção da galeria
- `tflite_flutter` para detecção básica de objetos
- Custom painters para desenhar as grades de composição
- Algoritmos de análise de luz ambiente
- Gerenciamento de permissões (câmera e galeria)

### 2. Tela de Edição Básica
**Arquivo:** `lib/screens/editing_screen.dart`

#### Ferramentas de Ajuste:
- **Brilho:** Slider (-100 a +100)
- **Contraste:** Slider (-100 a +100)
- **Saturação:** Slider (-100 a +100)
- **Nitidez:** Slider (0 a +200)

#### Presets Otimizados:
1. **"Doce Vivo"** - Cores vibrantes, alta saturação
2. **"Chocolate Profundo"** - Contraste alto, tons quentes
3. **"Fresco & Leve"** - Brilho alto, saturação suave
4. **"Clássico"** - Ajustes equilibrados
5. **"Vintage Doce"** - Tons sépia, contraste suave

#### Controles:
- Botão "Desfazer" (Ctrl+Z)
- Botão "Refazer" (Ctrl+Y)
- Botão "Redefinir" (volta ao original)
- Preview em tempo real

#### Componentes Técnicos:
- `image` package para manipulação de imagem
- Custom sliders com design iOS nativo
- Stack de histórico para undo/redo
- Processamento de imagem em background

### 3. Tela de Salvar/Exportar
**Arquivo:** `lib/screens/export_screen.dart`

#### Funcionalidades:
- **Preview Final:** Visualização da imagem editada
- **Salvar na Galeria:** Exportação para galeria do dispositivo
- **Qualidade:** Opção padrão de alta qualidade (90% JPEG)
- **Feedback Visual:** Loading e confirmação de salvamento

#### Componentes Técnicos:
- `gallery_saver` package para salvar na galeria
- Compressão otimizada de imagem
- Tratamento de erros de permissão

### 4. Sistema de Educação Fotográfica Integrado

#### **Onboarding Educativo**
**Arquivo:** `lib/screens/onboarding_screen.dart`

##### Conteúdo Educacional:
- **Slide 1:** "Transforme seus doces em arte"
  - Antes/Depois de uma foto de bolo com e sem guias
- **Slide 2:** "Aprenda enquanto fotografa"
  - Demonstração das guias de composição em ação
- **Slide 3:** "Dicas profissionais em tempo real"
  - Exemplo de sugestões de iluminação
- **Slide 4:** "Edição simples e poderosa"
  - Preview dos presets otimizados
- **Permissões:** Solicitação de acesso à câmera e galeria

#### **Centro de Aprendizado** (Tela Adicional)
**Arquivo:** `lib/screens/learning_center_screen.dart`

##### Seções Educativas:
1. **"Fundamentos da Fotografia de Doces"**
   - Regra dos terços explicada com exemplos
   - Importância da iluminação natural
   - Ângulos ideais para diferentes tipos de doce

2. **"Dicas por Tipo de Doce"**
   - 🧁 **Cupcakes:** "Fotografe em grupos ímpares (3, 5, 7)"
   - 🎂 **Bolos:** "Ângulo de 45° realça camadas e decoração"
   - 🍪 **Cookies:** "Luz lateral destaca textura e relevo"
   - 🍰 **Fatias:** "Foque nos detalhes internos e recheios"
   - 🍭 **Docinhos:** "Crie padrões e use cores contrastantes"

3. **"Erros Comuns e Como Evitar"**
   - ❌ "Foto muito escura" → ✅ "Use luz natural da janela"
   - ❌ "Doce no centro" → ✅ "Use a regra dos terços"
   - ❌ "Fundo bagunçado" → ✅ "Simplifique o background"
   - ❌ "Flash direto" → ✅ "Luz difusa ou natural"

4. **"Galeria de Inspiração"**
   - Exemplos de fotos bem-sucedidas
   - Análise do que torna cada foto especial
   - Técnicas utilizadas em cada exemplo

#### **Assistente Contextual Durante o Uso**
**Arquivo:** `lib/widgets/photo_assistant.dart`

##### Dicas Inteligentes em Tempo Real:
- **Análise Automática:** O app analisa a cena e oferece sugestões específicas
- **Tooltips Educativos:** Explicações rápidas sobre cada sugestão
- **Progresso de Aprendizado:** Sistema de conquistas para motivar o aprendizado

##### Exemplos de Sugestões Contextuais:
```
🎯 "Tente posicionar o bolo no ponto de intersecção da grade"
💡 "A luz está vindo da esquerda - isso criará sombras interessantes"
📐 "Para bolos altos, fotografe ligeiramente de cima para mostrar a decoração"
🌈 "As cores do seu doce ficam lindas com o preset 'Doce Vivo'"
⚡ "Dica rápida: Limpe a lente para fotos mais nítidas"
```

#### **Sistema de Feedback Educativo**
**Arquivo:** `lib/services/photo_feedback_service.dart`

##### Análise Pós-Captura:
- **Avaliação Automática:** Pontuação da foto baseada em composição, luz e foco
- **Sugestões de Melhoria:** "Que tal tentar com mais luz natural?"
- **Comparação Visual:** Mostrar como a mesma foto ficaria com ajustes sugeridos
- **Celebração de Acertos:** "Excelente uso da regra dos terços! 🎉"

---

## 🏗️ Arquitetura do Projeto

### Estrutura de Pastas
```
lib/
├── main.dart
├── app.dart
├── screens/
│   ├── onboarding_screen.dart
│   ├── camera_screen.dart
│   ├── editing_screen.dart
│   ├── export_screen.dart
│   └── learning_center_screen.dart
├── widgets/
│   ├── composition_guides.dart
│   ├── editing_controls.dart
│   ├── preset_buttons.dart
│   ├── custom_sliders.dart
│   ├── photo_assistant.dart
│   ├── smart_guides.dart
│   └── educational_tooltips.dart
├── models/
│   ├── image_edit_state.dart
│   └── preset_model.dart
├── services/
│   ├── camera_service.dart
│   ├── image_processing_service.dart
│   ├── storage_service.dart
│   ├── photo_feedback_service.dart
│   ├── lighting_analysis_service.dart
│   └── composition_analysis_service.dart
├── utils/
│   ├── constants.dart
│   ├── permissions.dart
│   └── image_utils.dart
└── theme/
    ├── app_theme.dart
    └── colors.dart
```

### Gerenciamento de Estado
- **Provider** para gerenciamento de estado simples
- **ChangeNotifier** para estados de edição de imagem
- **SharedPreferences** para configurações do usuário

---

## 🎨 Design System (Estilo iOS Nativo)

### Cores Principais
```dart
// Paleta inspirada em doces e confeitaria
static const Color primary = Color(0xFFE91E63); // Rosa doce
static const Color secondary = Color(0xFFFF9800); // Laranja caramelo
static const Color accent = Color(0xFF8BC34A); // Verde menta
static const Color background = Color(0xFFFAFAFA); // Branco açúcar
static const Color surface = Color(0xFFFFFFFF);
static const Color error = Color(0xFFE57373);
```

### Tipografia
- **Fonte Principal:** SF Pro Display (iOS) / Roboto (Android)
- **Tamanhos:** 
  - Título: 28pt, Bold
  - Subtítulo: 20pt, Medium
  - Corpo: 16pt, Regular
  - Caption: 12pt, Regular

### Componentes iOS
- **Botões:** Estilo iOS com bordas arredondadas (12px)
- **Sliders:** Design nativo iOS com thumb customizado
- **Cards:** Sombras suaves, bordas arredondadas (16px)
- **Navegação:** Navigation bar estilo iOS

---

## 📱 Fluxo de Usuário

### Primeira Utilização
1. **Splash Screen** (2s)
2. **Onboarding** (3 slides)
3. **Permissões** (câmera + galeria)
4. **Tela Principal** (câmera)

### Fluxo Principal com Aprendizado
1. **Tela Principal** → Receber guias inteligentes → Capturar/Selecionar imagem
2. **Feedback Imediato** → Análise da foto e sugestões de melhoria
3. **Tela de Edição** → Aplicar ajustes/presets com dicas contextuais
4. **Tela de Exportação** → Salvar na galeria + celebrar conquistas
5. **Retorno** → Tela Principal (novo ciclo com aprendizado acumulado)

### Fluxo de Aprendizado Paralelo
- **Centro de Aprendizado** → Acessível a qualquer momento via menu
- **Dicas Contextuais** → Aparecem durante o uso normal
- **Sistema de Conquistas** → Motiva o progresso fotográfico

### Navegação
- **Bottom Navigation:** Não utilizada no MVP
- **Stack Navigation:** Push/Pop entre telas
- **Gestos:** Swipe para voltar (iOS)

---

## 🔧 Dependências Principais

### Packages Essenciais
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # Câmera e Imagens
  camera: ^0.10.5
  image_picker: ^1.0.4
  image: ^4.1.3
  gallery_saver: ^2.3.2

  # IA e Análise de Imagem
  tflite_flutter: ^0.10.4
  tflite_flutter_helper: ^0.3.1
  
  # Estado e Navegação
  provider: ^6.1.1
  go_router: ^12.1.3
  
  # UI e Animações
  flutter_animate: ^4.2.0
  
  # Armazenamento
  shared_preferences: ^2.2.2
  
  # Permissões
  permission_handler: ^11.0.1
  
  # Utilitários
  path_provider: ^2.1.1
```

---

## ⚡ Performance e Otimizações

### Processamento de Imagem
- **Background Isolates** para edição pesada
- **Lazy Loading** de presets
- **Cache** de imagens processadas
- **Compressão** otimizada para mobile

### Memória
- **Dispose** adequado de controllers
- **Weak References** para imagens grandes
- **Garbage Collection** manual quando necessário

---

## 🧪 Estratégia de Testes

### Testes Unitários
- Serviços de processamento de imagem
- Modelos de dados
- Utilitários

### Testes de Widget
- Componentes de UI
- Interações básicas
- Estados de loading

### Testes de Integração
- Fluxo completo de captura → edição → salvamento
- Permissões
- Navegação entre telas

---

## 🚀 Critérios de Sucesso do MVP

### Métricas Técnicas
- **Tempo de inicialização:** < 3 segundos
- **Processamento de imagem:** < 2 segundos para ajustes básicos
- **Salvamento:** < 1 segundo para imagens até 5MB
- **Crash rate:** < 1%

### Métricas de Produto
- **Retenção D1:** > 40%
- **Conclusão do fluxo:** > 70%
- **Uso de presets:** > 60%
- **Feedback positivo:** > 4.0/5.0

---

## 📋 Próximos Passos

### Fase 1: Setup e Estrutura (Semana 1)
- [ ] Configuração do projeto Flutter
- [ ] Estrutura de pastas e arquitetura
- [ ] Design system e tema
- [ ] Navegação básica

### Fase 2: Funcionalidades Core (Semana 2-3)
- [ ] Tela de câmera com guias
- [ ] Seleção de galeria
- [ ] Processamento básico de imagem
- [ ] Tela de edição com sliders

### Fase 3: Polimento e Testes (Semana 4)
- [ ] Presets otimizados
- [ ] Onboarding
- [ ] Testes e correções
- [ ] Preparação para release

---

*Documento criado em: Dezembro 2024*
*Versão: 1.0 - MVP Specification*
