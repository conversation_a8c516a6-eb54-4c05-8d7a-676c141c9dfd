# Especificação Técnica - FotosApetitosas MVP
## App Flutter para Fotografia de Doces e Confeitaria

### 📋 Visão Geral
**Objetivo:** <PERSON><PERSON><PERSON> um MVP que prove o valor da ideia para confeiteiros que desejam tirar fotos melhores de seus produtos.

**Plataforma:** Flutter (iOS e Android)
**Público-alvo:** Confeiteiros, doceiros e profissionais de confeitaria
**Proposta de valor:** Simplificar o processo de captura e edição de fotos de doces com ferramentas otimizadas

---

## 🎯 Funcionalidades do MVP

### 1. Tela Principal (Câmera Integrada)
**Arquivo:** `lib/screens/camera_screen.dart`

#### Funcionalidades:
- **Captura de Imagem:** Acesso direto à câmera do dispositivo
- **Seleção de Galeria:** Importar fotos existentes da galeria
- **Guias de Composição:**
  - Regra dos Terços (grade 3x3)
  - Grade 1:1 (quadrada)
  - Toggle para mostrar/ocultar grades
- **Controles de Câmera:**
  - Botão de disparo principal
  - Troca entre câmera frontal/traseira
  - Flash on/off/auto

#### Componentes Técnicos:
- `camera` package para acesso à câmera
- `image_picker` package para seleção da galeria
- Custom painters para desenhar as grades de composição
- Gerenciamento de permissões (câmera e galeria)

### 2. Tela de Edição Básica
**Arquivo:** `lib/screens/editing_screen.dart`

#### Ferramentas de Ajuste:
- **Brilho:** Slider (-100 a +100)
- **Contraste:** Slider (-100 a +100)
- **Saturação:** Slider (-100 a +100)
- **Nitidez:** Slider (0 a +200)

#### Presets Otimizados:
1. **"Doce Vivo"** - Cores vibrantes, alta saturação
2. **"Chocolate Profundo"** - Contraste alto, tons quentes
3. **"Fresco & Leve"** - Brilho alto, saturação suave
4. **"Clássico"** - Ajustes equilibrados
5. **"Vintage Doce"** - Tons sépia, contraste suave

#### Controles:
- Botão "Desfazer" (Ctrl+Z)
- Botão "Refazer" (Ctrl+Y)
- Botão "Redefinir" (volta ao original)
- Preview em tempo real

#### Componentes Técnicos:
- `image` package para manipulação de imagem
- Custom sliders com design iOS nativo
- Stack de histórico para undo/redo
- Processamento de imagem em background

### 3. Tela de Salvar/Exportar
**Arquivo:** `lib/screens/export_screen.dart`

#### Funcionalidades:
- **Preview Final:** Visualização da imagem editada
- **Salvar na Galeria:** Exportação para galeria do dispositivo
- **Qualidade:** Opção padrão de alta qualidade (90% JPEG)
- **Feedback Visual:** Loading e confirmação de salvamento

#### Componentes Técnicos:
- `gallery_saver` package para salvar na galeria
- Compressão otimizada de imagem
- Tratamento de erros de permissão

### 4. Onboarding Inicial
**Arquivo:** `lib/screens/onboarding_screen.dart`

#### Conteúdo:
- **Slide 1:** "Transforme seus doces em arte"
- **Slide 2:** "Guias profissionais de composição"
- **Slide 3:** "Edição simples e poderosa"
- **Permissões:** Solicitação de acesso à câmera e galeria

---

## 🏗️ Arquitetura do Projeto

### Estrutura de Pastas
```
lib/
├── main.dart
├── app.dart
├── screens/
│   ├── onboarding_screen.dart
│   ├── camera_screen.dart
│   ├── editing_screen.dart
│   └── export_screen.dart
├── widgets/
│   ├── composition_guides.dart
│   ├── editing_controls.dart
│   ├── preset_buttons.dart
│   └── custom_sliders.dart
├── models/
│   ├── image_edit_state.dart
│   └── preset_model.dart
├── services/
│   ├── camera_service.dart
│   ├── image_processing_service.dart
│   └── storage_service.dart
├── utils/
│   ├── constants.dart
│   ├── permissions.dart
│   └── image_utils.dart
└── theme/
    ├── app_theme.dart
    └── colors.dart
```

### Gerenciamento de Estado
- **Provider** para gerenciamento de estado simples
- **ChangeNotifier** para estados de edição de imagem
- **SharedPreferences** para configurações do usuário

---

## 🎨 Design System (Estilo iOS Nativo)

### Cores Principais
```dart
// Paleta inspirada em doces e confeitaria
static const Color primary = Color(0xFFE91E63); // Rosa doce
static const Color secondary = Color(0xFFFF9800); // Laranja caramelo
static const Color accent = Color(0xFF8BC34A); // Verde menta
static const Color background = Color(0xFFFAFAFA); // Branco açúcar
static const Color surface = Color(0xFFFFFFFF);
static const Color error = Color(0xFFE57373);
```

### Tipografia
- **Fonte Principal:** SF Pro Display (iOS) / Roboto (Android)
- **Tamanhos:** 
  - Título: 28pt, Bold
  - Subtítulo: 20pt, Medium
  - Corpo: 16pt, Regular
  - Caption: 12pt, Regular

### Componentes iOS
- **Botões:** Estilo iOS com bordas arredondadas (12px)
- **Sliders:** Design nativo iOS com thumb customizado
- **Cards:** Sombras suaves, bordas arredondadas (16px)
- **Navegação:** Navigation bar estilo iOS

---

## 📱 Fluxo de Usuário

### Primeira Utilização
1. **Splash Screen** (2s)
2. **Onboarding** (3 slides)
3. **Permissões** (câmera + galeria)
4. **Tela Principal** (câmera)

### Fluxo Principal
1. **Tela Principal** → Capturar/Selecionar imagem
2. **Tela de Edição** → Aplicar ajustes/presets
3. **Tela de Exportação** → Salvar na galeria
4. **Retorno** → Tela Principal (novo ciclo)

### Navegação
- **Bottom Navigation:** Não utilizada no MVP
- **Stack Navigation:** Push/Pop entre telas
- **Gestos:** Swipe para voltar (iOS)

---

## 🔧 Dependências Principais

### Packages Essenciais
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # Câmera e Imagens
  camera: ^0.10.5
  image_picker: ^1.0.4
  image: ^4.1.3
  gallery_saver: ^2.3.2
  
  # Estado e Navegação
  provider: ^6.1.1
  go_router: ^12.1.3
  
  # UI e Animações
  flutter_animate: ^4.2.0
  
  # Armazenamento
  shared_preferences: ^2.2.2
  
  # Permissões
  permission_handler: ^11.0.1
  
  # Utilitários
  path_provider: ^2.1.1
```

---

## ⚡ Performance e Otimizações

### Processamento de Imagem
- **Background Isolates** para edição pesada
- **Lazy Loading** de presets
- **Cache** de imagens processadas
- **Compressão** otimizada para mobile

### Memória
- **Dispose** adequado de controllers
- **Weak References** para imagens grandes
- **Garbage Collection** manual quando necessário

---

## 🧪 Estratégia de Testes

### Testes Unitários
- Serviços de processamento de imagem
- Modelos de dados
- Utilitários

### Testes de Widget
- Componentes de UI
- Interações básicas
- Estados de loading

### Testes de Integração
- Fluxo completo de captura → edição → salvamento
- Permissões
- Navegação entre telas

---

## 🚀 Critérios de Sucesso do MVP

### Métricas Técnicas
- **Tempo de inicialização:** < 3 segundos
- **Processamento de imagem:** < 2 segundos para ajustes básicos
- **Salvamento:** < 1 segundo para imagens até 5MB
- **Crash rate:** < 1%

### Métricas de Produto
- **Retenção D1:** > 40%
- **Conclusão do fluxo:** > 70%
- **Uso de presets:** > 60%
- **Feedback positivo:** > 4.0/5.0

---

## 📋 Próximos Passos

### Fase 1: Setup e Estrutura (Semana 1)
- [ ] Configuração do projeto Flutter
- [ ] Estrutura de pastas e arquitetura
- [ ] Design system e tema
- [ ] Navegação básica

### Fase 2: Funcionalidades Core (Semana 2-3)
- [ ] Tela de câmera com guias
- [ ] Seleção de galeria
- [ ] Processamento básico de imagem
- [ ] Tela de edição com sliders

### Fase 3: Polimento e Testes (Semana 4)
- [ ] Presets otimizados
- [ ] Onboarding
- [ ] Testes e correções
- [ ] Preparação para release

---

*Documento criado em: Dezembro 2024*
*Versão: 1.0 - MVP Specification*
