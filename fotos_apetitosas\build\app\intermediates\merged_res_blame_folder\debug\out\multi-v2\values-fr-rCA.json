{"logs": [{"outputFile": "com.fotosapetitosas.fotos_apetitosas.app-mergeDebugResources-40:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a04377ab98aaf9189ccddcb951bae89\\transformed\\appcompat-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,922,1013,1105,1203,1298,1399,1492,1585,1680,1771,1862,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,80,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,318,428,515,621,751,836,917,1008,1100,1198,1293,1394,1487,1580,1675,1766,1857,1942,2052,2163,2266,2377,2485,2592,2751,2850,2936"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,922,1013,1105,1203,1298,1399,1492,1585,1680,1771,1862,1947,2057,2168,2271,2382,2490,2597,2756,3965", "endColumns": "110,106,109,86,105,129,84,80,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,318,428,515,621,751,836,917,1008,1100,1198,1293,1394,1487,1580,1675,1766,1857,1942,2052,2163,2266,2377,2485,2592,2751,2850,4046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2855,2953,3055,3154,3256,3360,3464,4051", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "2948,3050,3149,3251,3355,3459,3573,4147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3578,3648,3745,3822,4152,4321,4407", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "3643,3740,3817,3960,4316,4402,4482"}}]}]}