# This is a generated file; do not edit or check into version control.
camera=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\
camera_android=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\
camera_avfoundation=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\
camera_web=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_web-0.3.5\\
file_selector_linux=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\
file_selector_macos=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\
file_selector_windows=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\
flutter_plugin_android_lifecycle=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.26\\
gallery_saver=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\gallery_saver-2.3.2\\
image_picker=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker-1.1.2\\
image_picker_android=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_android-0.8.12+21\\
image_picker_for_web=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\
image_picker_ios=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\
image_picker_linux=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\
image_picker_macos=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\
image_picker_windows=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\
path_provider=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider-2.1.5\\
path_provider_android=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.15\\
path_provider_foundation=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\
path_provider_linux=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\
path_provider_windows=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\
permission_handler=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler-11.4.0\\
permission_handler_android=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_android-12.1.0\\
permission_handler_apple=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\
permission_handler_html=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\
permission_handler_windows=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\
shared_preferences=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\
shared_preferences_android=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\
shared_preferences_foundation=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\
shared_preferences_linux=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\
shared_preferences_web=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\
shared_preferences_windows=F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\
