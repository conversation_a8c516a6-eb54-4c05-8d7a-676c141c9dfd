import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:vibration/vibration.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';
import '../widgets/photo_assistant.dart';
import '../services/composition_analysis_service.dart';

/// Tela principal da câmera com guias inteligentes
class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _isFlashOn = false;
  bool _showGuides = true;
  bool _showAssistant = true;
  int _selectedCameraIndex = 0;
  CompositionAnalysis? _liveCompositionAnalysis;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        await _setupCamera(_cameras![_selectedCameraIndex]);
      }
    } catch (e) {
      debugPrint('Erro ao inicializar câmera: $e');
    }
  }

  Future<void> _setupCamera(CameraDescription camera) async {
    _cameraController = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await _cameraController!.initialize();
      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('Erro ao configurar câmera: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Preview da câmera
            _buildCameraPreview(),

            // Guias de composição
            if (_showGuides) _buildCompositionGuides(),

            // Header com controles
            _buildHeader(),

            // Footer com controles de captura
            _buildFooter(),

            // Assistente fotográfico
            if (_showAssistant)
              Positioned(
                top: 100,
                left: AppSpacing.md,
                right: AppSpacing.md,
                child: PhotoAssistant(
                  isVisible: _showAssistant,
                  onTipTap: () => _provideFeedback(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (!_isCameraInitialized || _cameraController == null) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    return SizedBox.expand(
      child: FittedBox(
        fit: BoxFit.cover,
        child: SizedBox(
          width: _cameraController!.value.previewSize?.height ?? 0,
          height: _cameraController!.value.previewSize?.width ?? 0,
          child: CameraPreview(_cameraController!),
        ),
      ),
    );
  }

  Widget _buildCompositionGuides() {
    return CustomPaint(
      size: Size.infinite,
      painter: CompositionGuidesPainter(),
    );
  }

  Widget _buildHeader() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.6),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Botão de menu/aprendizado
            IconButton(
              onPressed: () => context.go('/learning'),
              icon: const Icon(
                Icons.school_outlined,
                color: Colors.white,
                size: 28,
              ),
            ),

            // Título
            Text(
              'FotosApetitosas',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
            ),

            // Controles da câmera
            Row(
              children: [
                // Toggle das guias
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showGuides = !_showGuides;
                    });
                    _provideFeedback();
                  },
                  icon: Icon(
                    _showGuides ? Icons.grid_on : Icons.grid_off,
                    color: Colors.white,
                    size: 28,
                  ),
                ),

                // Toggle do assistente
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showAssistant = !_showAssistant;
                    });
                    _provideFeedback();
                  },
                  icon: Icon(
                    _showAssistant ? Icons.assistant : Icons.assistant_outlined,
                    color: Colors.white,
                    size: 28,
                  ),
                ),

                // Flash
                IconButton(
                  onPressed: _toggleFlash,
                  icon: Icon(
                    _isFlashOn ? Icons.flash_on : Icons.flash_off,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.8),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Galeria
            IconButton(
              onPressed: _pickFromGallery,
              icon: const Icon(
                Icons.photo_library_outlined,
                color: Colors.white,
                size: 32,
              ),
            ),

            // Botão de captura
            GestureDetector(
              onTap: _takePicture,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 4,
                  ),
                ),
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),

            // Trocar câmera
            IconButton(
              onPressed: _switchCamera,
              icon: const Icon(
                Icons.flip_camera_ios_outlined,
                color: Colors.white,
                size: 32,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _takePicture() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      final XFile image = await _cameraController!.takePicture();
      if (mounted) {
        context.go('/editing', extra: image.path);
      }
    } catch (e) {
      debugPrint('Erro ao tirar foto: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao tirar foto: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _pickFromGallery() async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null && mounted) {
        context.go('/editing', extra: image.path);
      }
    } catch (e) {
      debugPrint('Erro ao selecionar da galeria: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao selecionar imagem: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleFlash() async {
    if (_cameraController == null) return;

    try {
      setState(() {
        _isFlashOn = !_isFlashOn;
      });

      await _cameraController!.setFlashMode(
        _isFlashOn ? FlashMode.torch : FlashMode.off,
      );
    } catch (e) {
      debugPrint('Erro ao alterar flash: $e');
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    setState(() {
      _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras!.length;
      _isCameraInitialized = false;
    });

    await _cameraController?.dispose();
    await _setupCamera(_cameras![_selectedCameraIndex]);
  }

  /// Fornece feedback háptico ao usuário
  Future<void> _provideFeedback() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        Vibration.vibrate(duration: 50);
      }
    } catch (e) {
      debugPrint('Erro no feedback háptico: $e');
    }
  }
}

/// Painter para desenhar as guias de composição
class CompositionGuidesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.gridLines
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Regra dos terços - linhas verticais
    final verticalSpacing = size.width / 3;
    for (int i = 1; i < 3; i++) {
      canvas.drawLine(
        Offset(verticalSpacing * i, 0),
        Offset(verticalSpacing * i, size.height),
        paint,
      );
    }

    // Regra dos terços - linhas horizontais
    final horizontalSpacing = size.height / 3;
    for (int i = 1; i < 3; i++) {
      canvas.drawLine(
        Offset(0, horizontalSpacing * i),
        Offset(size.width, horizontalSpacing * i),
        paint,
      );
    }

    // Pontos de interesse
    final pointPaint = Paint()
      ..color = AppColors.focusIndicator
      ..style = PaintingStyle.fill;

    final points = [
      Offset(verticalSpacing, horizontalSpacing),
      Offset(verticalSpacing * 2, horizontalSpacing),
      Offset(verticalSpacing, horizontalSpacing * 2),
      Offset(verticalSpacing * 2, horizontalSpacing * 2),
    ];

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
