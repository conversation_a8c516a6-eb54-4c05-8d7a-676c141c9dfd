name: fotos_apetitosas
description: "App para ajudar confeiteiros a tirarem fotos profissionais de seus doces"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter

  # UI e Ícones
  cupertino_icons: ^1.0.8

  # Câmera e Imagens
  camera: ^0.10.5
  image_picker: ^1.0.4
  image: ^4.1.3
  # image_gallery_saver: ^2.0.3  # Temporariamente removido para compatibilidade

  # IA e Análise de Imagem (será implementado gradualmente)
  # tflite_flutter: ^0.9.0
  # tflite_flutter_helper: ^0.3.1

  # Processamento de Imagem Avançado
  # image_editor_plus: ^5.0.2  # Será implementado gradualmente
  # flutter_image_filters: ^1.0.1  # Não disponível, usaremos alternativa

  # Estado e Navegação
  provider: ^6.1.1
  go_router: ^12.1.3

  # UI e Animações
  flutter_animate: ^4.2.0

  # Armazenamento
  shared_preferences: ^2.2.2

  # Permissões
  permission_handler: ^11.0.1

  # Utilitários
  path_provider: ^2.1.1

  # Feedback e Interação
  vibration: ^2.0.0
  flutter_local_notifications: ^18.0.1

  # Performance e Cache
  cached_network_image: ^3.4.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Processamento de Imagem Nativo (implementação customizada)
  # photofilters: ^3.0.3  # Não disponível, implementaremos customizado

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
