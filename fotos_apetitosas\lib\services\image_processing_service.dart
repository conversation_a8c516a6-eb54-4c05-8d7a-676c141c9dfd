import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

/// Serviço para processamento avançado de imagens
class ImageProcessingService {
  static final ImageProcessingService _instance =
      ImageProcessingService._internal();
  factory ImageProcessingService() => _instance;
  ImageProcessingService._internal();

  /// Aplica ajustes de brilho, contraste, saturação e nitidez em uma imagem
  Future<String> processImage({
    required String imagePath,
    double brightness = 0.0,
    double contrast = 0.0,
    double saturation = 0.0,
    double sharpness = 0.0,
  }) async {
    try {
      // Carrega a imagem
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Não foi possível decodificar a imagem');
      }

      // Aplica os ajustes
      image = _applyBrightness(image, brightness);
      image = _applyContrast(image, contrast);
      image = _applySaturation(image, saturation);
      image = _applySharpness(image, sharpness);

      // Salva a imagem processada
      final processedPath = await _saveProcessedImage(image);
      return processedPath;
    } catch (e) {
      debugPrint('Erro ao processar imagem: $e');
      return imagePath; // Retorna a imagem original em caso de erro
    }
  }

  /// Aplica preset específico na imagem
  Future<String> applyPreset({
    required String imagePath,
    required String presetName,
  }) async {
    final presets = {
      'Doce Vivo': {
        'brightness': 10.0,
        'contrast': 15.0,
        'saturation': 25.0,
        'sharpness': 10.0,
      },
      'Chocolate Profundo': {
        'brightness': -5.0,
        'contrast': 20.0,
        'saturation': 10.0,
        'sharpness': 15.0,
      },
      'Fresco & Leve': {
        'brightness': 15.0,
        'contrast': 5.0,
        'saturation': 15.0,
        'sharpness': 5.0,
      },
      'Clássico': {
        'brightness': 5.0,
        'contrast': 10.0,
        'saturation': 5.0,
        'sharpness': 8.0,
      },
      'Vintage Doce': {
        'brightness': 5.0,
        'contrast': 10.0,
        'saturation': -10.0,
        'sharpness': 0.0,
      },
    };

    final preset = presets[presetName];
    if (preset == null) {
      return imagePath;
    }

    return processImage(
      imagePath: imagePath,
      brightness: preset['brightness']!,
      contrast: preset['contrast']!,
      saturation: preset['saturation']!,
      sharpness: preset['sharpness']!,
    );
  }

  /// Aplica ajuste de brilho
  img.Image _applyBrightness(img.Image image, double brightness) {
    if (brightness == 0.0) return image;

    final factor = brightness / 100.0;
    return img.adjustColor(image, brightness: factor);
  }

  /// Aplica ajuste de contraste
  img.Image _applyContrast(img.Image image, double contrast) {
    if (contrast == 0.0) return image;

    final factor = (contrast + 100.0) / 100.0;
    return img.adjustColor(image, contrast: factor);
  }

  /// Aplica ajuste de saturação
  img.Image _applySaturation(img.Image image, double saturation) {
    if (saturation == 0.0) return image;

    final factor = (saturation + 100.0) / 100.0;
    return img.adjustColor(image, saturation: factor);
  }

  /// Aplica ajuste de nitidez
  img.Image _applySharpness(img.Image image, double sharpness) {
    if (sharpness == 0.0) return image;

    // Converte o valor de sharpness para um fator apropriado
    final factor = sharpness / 100.0;

    // Aplica filtro de nitidez usando convolução
    if (factor > 0) {
      // Kernel de nitidez
      final kernel = [
        [0.0, -factor, 0.0],
        [-factor, 1 + 4 * factor, -factor],
        [0.0, -factor, 0.0],
      ];

      return _applyConvolution(image, kernel);
    }

    return image;
  }

  /// Aplica convolução com kernel personalizado
  img.Image _applyConvolution(img.Image image, List<List<double>> kernel) {
    final result = img.Image.from(image);
    final width = image.width;
    final height = image.height;

    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        double r = 0, g = 0, b = 0;

        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final pixel = image.getPixel(x + kx - 1, y + ky - 1);
            final weight = kernel[ky][kx];

            r += pixel.r * weight;
            g += pixel.g * weight;
            b += pixel.b * weight;
          }
        }

        // Clamp values
        r = r.clamp(0, 255);
        g = g.clamp(0, 255);
        b = b.clamp(0, 255);

        final originalPixel = image.getPixel(x, y);
        final alpha = originalPixel.a.toInt();
        result.setPixel(
            x, y, img.ColorRgba8(r.toInt(), g.toInt(), b.toInt(), alpha));
      }
    }

    return result;
  }

  /// Salva a imagem processada em um arquivo temporário
  Future<String> _saveProcessedImage(img.Image image) async {
    final directory = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/processed_$timestamp.jpg';

    final encodedImage = img.encodeJpg(image, quality: 90);
    final file = File(filePath);
    await file.writeAsBytes(encodedImage);

    return filePath;
  }

  /// Analisa a qualidade da iluminação de uma imagem
  Future<LightingAnalysis> analyzeLighting(String imagePath) async {
    try {
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        return LightingAnalysis.unknown();
      }

      // Calcula a luminância média
      double totalLuminance = 0;
      int pixelCount = 0;

      // Amostra pixels para performance (a cada 10 pixels)
      for (int y = 0; y < image.height; y += 10) {
        for (int x = 0; x < image.width; x += 10) {
          final pixel = image.getPixel(x, y);
          final r = pixel.r;
          final g = pixel.g;
          final b = pixel.b;

          // Fórmula de luminância
          final luminance = 0.299 * r + 0.587 * g + 0.114 * b;
          totalLuminance += luminance;
          pixelCount++;
        }
      }

      final averageLuminance = totalLuminance / pixelCount;

      return LightingAnalysis.fromLuminance(averageLuminance);
    } catch (e) {
      debugPrint('Erro ao analisar iluminação: $e');
      return LightingAnalysis.unknown();
    }
  }

  /// Detecta o tipo de doce na imagem (implementação básica)
  Future<SweetType> detectSweetType(String imagePath) async {
    // Por enquanto, retorna um tipo aleatório para demonstração
    // Na implementação real, usaríamos TensorFlow Lite
    final types = [
      SweetType.cupcake,
      SweetType.cake,
      SweetType.cookie,
      SweetType.candy,
      SweetType.unknown,
    ];

    // Simula análise baseada no nome do arquivo ou outras heurísticas
    final fileName = imagePath.toLowerCase();
    if (fileName.contains('cupcake') || fileName.contains('muffin')) {
      return SweetType.cupcake;
    } else if (fileName.contains('cake') || fileName.contains('bolo')) {
      return SweetType.cake;
    } else if (fileName.contains('cookie') || fileName.contains('biscoito')) {
      return SweetType.cookie;
    } else if (fileName.contains('candy') || fileName.contains('doce')) {
      return SweetType.candy;
    }

    return types[DateTime.now().millisecond % types.length];
  }
}

/// Análise de iluminação da imagem
class LightingAnalysis {
  final LightingQuality quality;
  final double luminance;
  final String suggestion;
  final IconData icon;
  final Color color;

  const LightingAnalysis({
    required this.quality,
    required this.luminance,
    required this.suggestion,
    required this.icon,
    required this.color,
  });

  factory LightingAnalysis.fromLuminance(double luminance) {
    if (luminance < 50) {
      return LightingAnalysis(
        quality: LightingQuality.tooLow,
        luminance: luminance,
        suggestion: 'Muito escuro - tente mais luz natural ou mude de posição',
        icon: Icons.brightness_low,
        color: Colors.red,
      );
    } else if (luminance < 100) {
      return LightingAnalysis(
        quality: LightingQuality.low,
        luminance: luminance,
        suggestion: 'Pouca luz - aproxime-se de uma janela',
        icon: Icons.brightness_medium,
        color: Colors.orange,
      );
    } else if (luminance < 180) {
      return LightingAnalysis(
        quality: LightingQuality.good,
        luminance: luminance,
        suggestion: 'Ótima iluminação! Perfeito para destacar cores',
        icon: Icons.wb_sunny,
        color: Colors.green,
      );
    } else if (luminance < 220) {
      return LightingAnalysis(
        quality: LightingQuality.high,
        luminance: luminance,
        suggestion: 'Luz forte - cuidado com sombras muito marcadas',
        icon: Icons.brightness_high,
        color: Colors.orange,
      );
    } else {
      return LightingAnalysis(
        quality: LightingQuality.tooHigh,
        luminance: luminance,
        suggestion: 'Muito claro - tente um ângulo diferente',
        icon: Icons.brightness_7,
        color: Colors.red,
      );
    }
  }

  factory LightingAnalysis.unknown() {
    return const LightingAnalysis(
      quality: LightingQuality.unknown,
      luminance: 0,
      suggestion: 'Não foi possível analisar a iluminação',
      icon: Icons.help_outline,
      color: Colors.grey,
    );
  }
}

/// Qualidade da iluminação
enum LightingQuality {
  tooLow,
  low,
  good,
  high,
  tooHigh,
  unknown,
}

/// Tipos de doces detectáveis
enum SweetType {
  cupcake,
  cake,
  cookie,
  candy,
  unknown,
}

/// Extensão para obter informações sobre tipos de doces
extension SweetTypeExtension on SweetType {
  String get name {
    switch (this) {
      case SweetType.cupcake:
        return 'Cupcake';
      case SweetType.cake:
        return 'Bolo';
      case SweetType.cookie:
        return 'Cookie';
      case SweetType.candy:
        return 'Docinho';
      case SweetType.unknown:
        return 'Doce';
    }
  }

  String get emoji {
    switch (this) {
      case SweetType.cupcake:
        return '🧁';
      case SweetType.cake:
        return '🎂';
      case SweetType.cookie:
        return '🍪';
      case SweetType.candy:
        return '🍭';
      case SweetType.unknown:
        return '🍰';
    }
  }

  List<String> get tips {
    switch (this) {
      case SweetType.cupcake:
        return [
          'Fotografe em grupos ímpares (3, 5, 7)',
          'Destaque o frosting e decorações',
          'Use luz lateral para criar textura',
        ];
      case SweetType.cake:
        return [
          'Ângulo de 45° realça camadas e decoração',
          'Mostre a altura e os detalhes',
          'Capture fatias para mostrar o interior',
        ];
      case SweetType.cookie:
        return [
          'Luz lateral destaca textura e relevo',
          'Agrupe de forma orgânica',
          'Mostre diferentes formatos juntos',
        ];
      case SweetType.candy:
        return [
          'Crie padrões e use cores contrastantes',
          'Fotografe de cima para mostrar variedade',
          'Use props que complementem as cores',
        ];
      case SweetType.unknown:
        return [
          'Use a regra dos terços para composição',
          'Mantenha o fundo limpo e simples',
          'Aproveite a luz natural sempre que possível',
        ];
    }
  }
}
