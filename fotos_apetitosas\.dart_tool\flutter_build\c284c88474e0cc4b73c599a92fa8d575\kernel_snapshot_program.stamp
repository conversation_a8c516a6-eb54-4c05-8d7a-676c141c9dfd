{"inputs": ["F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\.dart_tool\\package_config_subset", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "f:\\Projetos\\fvm\\versions\\stable\\bin\\internal\\engine.version", "f:\\Projetos\\fvm\\versions\\stable\\bin\\internal\\engine.version", "f:\\Projetos\\fvm\\versions\\stable\\bin\\internal\\engine.version", "f:\\Projetos\\fvm\\versions\\stable\\bin\\internal\\engine.version", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\main.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\material.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\services.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\app.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\lib\\camera_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_android-0.8.12+21\\lib\\image_picker_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.15\\lib\\path_provider_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\shared_preferences_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\lib\\camera_avfoundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\lib\\image_picker_ios.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\lib\\file_selector_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\lib\\image_picker_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\lib\\file_selector_macos.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\lib\\image_picker_macos.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\lib\\file_selector_windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\lib\\image_picker_windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\about.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\app.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\arc.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\badge.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\banner.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button_style.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\card.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\carousel.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\chip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\colors.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\constants.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\curves.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\data_table.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\date.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\dialog.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\divider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\drawer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\flutter_logo.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\icons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\input_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\material.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\material_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\material_state.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\motion.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\page.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\radio.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\search.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\shadows.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\slider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\stepper.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\switch.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tabs.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_field.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\time.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\typography.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\widgets.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\autofill.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\flavor.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\live_text.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\process_text.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\restoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_input.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\go_router.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\provider.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\theme\\app_theme.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\screens\\onboarding_screen.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\screens\\camera_screen.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\screens\\editing_screen.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\screens\\export_screen.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\screens\\learning_center_screen.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\lib\\src\\android_camera.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\image_picker_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_android-0.8.12+21\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.15\\lib\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\src\\shared_preferences_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\src\\shared_preferences_async_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\lib\\src\\avfoundation_camera.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\file_selector_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\path.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\scheduler.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\back_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\cupertino.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\rendering.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\animation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\gestures.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\painting.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\characters.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\app.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\async.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\container.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\form.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\image.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\router.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\table.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\text.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\title.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\builder.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\configuration.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\delegate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\information_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\match.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\errors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\inherited_router.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\custom_transition_page.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\parser.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\route.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\route_data.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\router.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\state.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\async_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\consumer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\selector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\lib\\theme\\colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\flutter_animate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler-11.4.0\\lib\\permission_handler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\lib\\camera.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker-1.1.2\\lib\\image_picker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\camera_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\stream_transform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\lib\\src\\type_conversion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.15.0\\lib\\meta.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\key.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\node.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\object.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\print.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\cross_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\platform_interface\\image_picker_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\src\\strings.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\lib\\src\\messages_async.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\lib\\src\\type_conversion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\platform_interface\\file_selector_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\context.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_exception.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\lib\\ffi.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\semantics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\box.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\error.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\image.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\object.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\table.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\view.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\animation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\animations.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\curves.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\tween.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\events.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\team.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\borders.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\clip.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\colors.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\extensions.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\physics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\collection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.15.0\\lib\\meta_meta.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\logging.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\misc\\error_screen.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\cupertino.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\pages\\material.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\lib\\src\\path_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\logging-1.3.0\\lib\\logging.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\lib\\nested.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\devtool.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\animate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\animate_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effect_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\flutter_animate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\adapters.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\effects.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\extensions\\extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\permission_handler_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\lib\\src\\camera_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\lib\\src\\camera_image.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\lib\\src\\camera_preview.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\events\\camera_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\events\\device_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\platform_interface\\camera_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\media_settings.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\async_expand.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\async_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\combine_latest.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\concatenate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\merge.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\rate_limit.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\scan.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\switch.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\take_until.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\tap.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\where.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\x_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\method_channel\\method_channel_image_picker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\camera_delegate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\camera_device.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\image_options.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\image_source.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\lost_data_response.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\media_options.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\media_selection_type.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\multi_image_picker_options.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\picked_file\\picked_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\retrieve_type.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\method_channel\\method_channel_file_selector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\file_dialog_options.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\file_save_location.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\x_type_group.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\characters.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\internal_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\parsed_path.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\posix.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\url.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\allocation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\arena.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf16.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf8.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters_impl.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\physics\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\algorithms.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\boollist.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\canonicalized_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterable.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\comparators.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\functions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_zip.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\list_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\priority_queue.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\queue_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\unmodifiable_wrappers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\wrappers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\level.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\log_record.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\logger.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\warn.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\scroll_adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\value_notifier_adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\change_notifier_adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\adapters\\value_adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\align_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\blur_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\box_shadow_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\callback_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\color_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\crossfade_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\custom_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\elevation_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\fade_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\flip_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\follow_path_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\listen_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\move_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\rotate_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\saturate_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\scale_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\shader_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\shake_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\shimmer_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\slide_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\swap_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\then_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\tint_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\toggle_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\effects\\visibility_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\extensions\\animation_controller_loop_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\extensions\\num_duration_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\lib\\src\\extensions\\offset_copy_with_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\method_channel_permission_handler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_handler_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_status.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permissions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\service_status.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\utils\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\method_channel\\method_channel_camera.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\resolution_preset.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\camera_description.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\camera_exception.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\camera_image_data.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\exposure_mode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\flash_mode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\focus_mode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\image_file_format.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\image_format_group.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\types\\video_capture_options.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\common_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\aggregate_sample.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\lib\\src\\from_handlers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\types\\io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\picked_file\\lost_data.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\picked_file\\io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\table.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\breaks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\empty_unmodifiable_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\lib\\flutter_shaders.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\utils\\codec.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\lib\\src\\method_channel\\type_conversion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\types\\base.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\lib\\src\\types\\picked_file\\base.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\lib\\src\\animated_sampler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\lib\\src\\shader_builder.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\lib\\src\\inkwell_shader.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\lib\\src\\set_uniforms.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\error_helpers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\frustum.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\intersection_result.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\noise.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\obb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\opengl.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\plane.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quaternion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\ray.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\sphere.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\triangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\utilities.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector4.dart"], "outputs": ["F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\.dart_tool\\flutter_build\\c284c88474e0cc4b73c599a92fa8d575\\program.dill", "F:\\Projetos\\02 - Dev\\guiadefoto\\fotos_apetitosas\\.dart_tool\\flutter_build\\c284c88474e0cc4b73c599a92fa8d575\\program.dill"]}