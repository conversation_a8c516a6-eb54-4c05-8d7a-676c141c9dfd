import 'package:flutter/material.dart';

/// Paleta de cores inspirada em doces e confeitaria
/// <PERSON><PERSON><PERSON> as diretrizes do Apple Human Interface Guidelines
class AppColors {
  // Cores Primárias
  static const Color primary = Color(0xFFE91E63); // Rosa doce
  static const Color primaryLight = Color(0xFFFF6090); // Rosa claro
  static const Color primaryDark = Color(0xFFB0003A); // Rosa escuro
  
  // Cores Secundárias
  static const Color secondary = Color(0xFFFF9800); // Laranja caramelo
  static const Color secondaryLight = Color(0xFFFFC947); // Laranja claro
  static const Color secondaryDark = Color(0xFFC66900); // Laranja escuro
  
  // Cores de Destaque
  static const Color accent = Color(0xFF8BC34A); // Verde menta
  static const Color accentLight = Color(0xFFBCF5A9); // Verde claro
  static const Color accentDark = Color(0xFF5A9216); // Verde escuro
  
  // Cores de Fundo
  static const Color background = Color(0xFFFAFAFA); // Branco açúcar
  static const Color surface = Color(0xFFFFFFFF); // Branco puro
  static const Color surfaceVariant = Color(0xFFF5F5F5); // Cinza muito claro
  
  // Cores de Texto
  static const Color onPrimary = Color(0xFFFFFFFF); // Branco
  static const Color onSecondary = Color(0xFFFFFFFF); // Branco
  static const Color onBackground = Color(0xFF1C1B1F); // Preto suave
  static const Color onSurface = Color(0xFF1C1B1F); // Preto suave
  static const Color onSurfaceVariant = Color(0xFF49454F); // Cinza escuro
  
  // Cores de Estado
  static const Color error = Color(0xFFE57373); // Vermelho suave
  static const Color onError = Color(0xFFFFFFFF); // Branco
  static const Color success = Color(0xFF66BB6A); // Verde sucesso
  static const Color warning = Color(0xFFFFB74D); // Amarelo aviso
  static const Color info = Color(0xFF42A5F5); // Azul informação
  
  // Cores de Overlay
  static const Color overlay = Color(0x80000000); // Preto transparente
  static const Color overlayLight = Color(0x40000000); // Preto mais transparente
  
  // Cores de Sombra (iOS Style)
  static const Color shadow = Color(0x1A000000); // Sombra suave
  static const Color shadowMedium = Color(0x33000000); // Sombra média
  static const Color shadowStrong = Color(0x4D000000); // Sombra forte
  
  // Cores Específicas para Fotografia
  static const Color cameraOverlay = Color(0x99000000); // Overlay da câmera
  static const Color gridLines = Color(0x66FFFFFF); // Linhas de grade
  static const Color focusIndicator = Color(0xFFFFD700); // Indicador de foco dourado
  
  // Gradientes para Presets
  static const LinearGradient sweetGradient = LinearGradient(
    colors: [Color(0xFFFF6B9D), Color(0xFFC44569)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient chocolateGradient = LinearGradient(
    colors: [Color(0xFF8B4513), Color(0xFF5D2F02)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient freshGradient = LinearGradient(
    colors: [Color(0xFF98FB98), Color(0xFF90EE90)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient classicGradient = LinearGradient(
    colors: [Color(0xFFF5F5DC), Color(0xFFDDD8C0)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient vintageGradient = LinearGradient(
    colors: [Color(0xFFDEB887), Color(0xFFCD853F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

/// Extensão para facilitar o uso das cores
extension AppColorsExtension on BuildContext {
  AppColors get colors => AppColors();
}
