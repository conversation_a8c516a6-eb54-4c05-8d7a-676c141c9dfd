{"logs": [{"outputFile": "com.fotosapetitosas.fotos_apetitosas.app-mergeDebugResources-40:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a04377ab98aaf9189ccddcb951bae89\\transformed\\appcompat-1.1.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,907,998,1090,1185,1279,1381,1474,1569,1666,1757,1850,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,78,90,91,94,93,101,92,94,96,90,92,79,105,103,97,105,103,101,153,96,80", "endOffsets": "214,314,426,512,618,741,823,902,993,1085,1180,1274,1376,1469,1564,1661,1752,1845,1925,2031,2135,2233,2339,2443,2545,2699,2796,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,907,998,1090,1185,1279,1381,1474,1569,1666,1757,1850,1930,2036,2140,2238,2344,2448,2550,2704,3931", "endColumns": "113,99,111,85,105,122,81,78,90,91,94,93,101,92,94,96,90,92,79,105,103,97,105,103,101,153,96,80", "endOffsets": "214,314,426,512,618,741,823,902,993,1085,1180,1274,1376,1469,1564,1661,1752,1845,1925,2031,2135,2233,2339,2443,2545,2699,2796,4007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2801,2900,3002,3100,3197,3305,3416,4012", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "2895,2997,3095,3192,3300,3411,3533,4108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3538,3607,3699,3783,4113,4282,4366", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "3602,3694,3778,3926,4277,4361,4440"}}]}]}