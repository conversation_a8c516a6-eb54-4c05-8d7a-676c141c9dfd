 F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z:  F:\\Projetos\\02\ -\ Dev\\guiadefoto\\fotos_apetitosas\\pubspec.yaml F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf f:\\Projetos\\fvm\\versions\\stable\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\archive-4.0.7\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\args-2.7.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\async-2.11.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cached_network_image-3.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cached_network_image_platform_interface-4.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cached_network_image_web-1.3.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera-0.10.6\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_android-0.10.10\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_avfoundation-0.9.19\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_platform_interface-2.10.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\camera_web-0.3.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.3.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\clock-1.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.18.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\device_info_plus-11.3.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_animate-4.5.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_lints-4.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_local_notifications-18.0.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_local_notifications_linux-5.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-8.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.26\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_shaders-0.1.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\go_router-12.1.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\hive-2.2.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\hive_flutter-1.1.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http-1.4.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http_parser-4.0.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image-4.5.4\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_android-0.8.12+21\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker-10.0.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\lints-4.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\logging-1.3.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.15.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\mime-2.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.15\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\petitparser-6.0.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\posix-6.0.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\rxdart-0.28.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.7\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sqflite-2.4.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sqflite_android-2.4.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sqflite_common-2.5.4+6\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sqflite_darwin-2.4.1+1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stack_trace-1.11.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\string_scanner-1.2.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\synchronized-3.3.0+3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\test_api-0.7.2\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\timezone-0.10.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vibration-2.1.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vibration_platform_interface-0.0.3\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vm_service-14.2.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\web-1.1.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\win32-5.10.1\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\win32_registry-1.1.5\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xml-6.5.0\\LICENSE f:\\Projetos\\fvm\\versions\\stable\\bin\\cache\\pkg\\sky_engine\\LICENSE f:\\Projetos\\fvm\\versions\\stable\\packages\\flutter\\LICENSE