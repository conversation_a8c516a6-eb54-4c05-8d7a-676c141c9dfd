  ActivityManager android.app  getISLowRamDevice android.app.ActivityManager  getIsLowRamDevice android.app.ActivityManager  isLowRamDevice android.app.ActivityManager  setLowRamDevice android.app.ActivityManager  ContentResolver android.content  Context android.content  ACTIVITY_SERVICE android.content.Context  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getPACKAGEManager android.content.Context  getPackageManager android.content.Context  getSystemService android.content.Context  packageManager android.content.Context  setContentResolver android.content.Context  setPackageManager android.content.Context  FeatureInfo android.content.pm  PackageManager android.content.pm  name android.content.pm.FeatureInfo  getSYSTEMAvailableFeatures !android.content.pm.PackageManager  getSystemAvailableFeatures !android.content.pm.PackageManager  setSystemAvailableFeatures !android.content.pm.PackageManager  systemAvailableFeatures !android.content.pm.PackageManager  Build 
android.os  BOARD android.os.Build  
BOOTLOADER android.os.Build  BRAND android.os.Build  DEVICE android.os.Build  DISPLAY android.os.Build  FINGERPRINT android.os.Build  HARDWARE android.os.Build  HOST android.os.Build  ID android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  SERIAL android.os.Build  SUPPORTED_32_BIT_ABIS android.os.Build  SUPPORTED_64_BIT_ABIS android.os.Build  SUPPORTED_ABIS android.os.Build  TAGS android.os.Build  TYPE android.os.Build  UNKNOWN android.os.Build  VERSION android.os.Build  
VERSION_CODES android.os.Build  	getSerial android.os.Build  BASE_OS android.os.Build.VERSION  CODENAME android.os.Build.VERSION  INCREMENTAL android.os.Build.VERSION  PREVIEW_SDK_INT android.os.Build.VERSION  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  SECURITY_PATCH android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  N_MR1 android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Settings android.provider  Global android.provider.Settings  DEVICE_NAME  android.provider.Settings.Global  	getString  android.provider.Settings.Global  Any %dev.fluttercommunity.plus.device_info  Array %dev.fluttercommunity.plus.device_info  Boolean %dev.fluttercommunity.plus.device_info  Build %dev.fluttercommunity.plus.device_info  Context %dev.fluttercommunity.plus.device_info  DeviceInfoPlusPlugin %dev.fluttercommunity.plus.device_info  HashMap %dev.fluttercommunity.plus.device_info  List %dev.fluttercommunity.plus.device_info  MethodCallHandlerImpl %dev.fluttercommunity.plus.device_info  
MethodChannel %dev.fluttercommunity.plus.device_info  
MutableMap %dev.fluttercommunity.plus.device_info  SecurityException %dev.fluttercommunity.plus.device_info  Settings %dev.fluttercommunity.plus.device_info  String %dev.fluttercommunity.plus.device_info  Suppress %dev.fluttercommunity.plus.device_info  contains %dev.fluttercommunity.plus.device_info  	emptyList %dev.fluttercommunity.plus.device_info  	filterNot %dev.fluttercommunity.plus.device_info  listOf %dev.fluttercommunity.plus.device_info  map %dev.fluttercommunity.plus.device_info  set %dev.fluttercommunity.plus.device_info  
startsWith %dev.fluttercommunity.plus.device_info  ActivityManager :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  BinaryMessenger :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  Context :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  
FlutterPlugin :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  MethodCallHandlerImpl :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  
MethodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  PackageManager :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  
methodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  setupMethodChannel :dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin  ActivityManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Any ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Array ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Boolean ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Build ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  ContentResolver ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  FeatureInfo ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  HashMap ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  List ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
MethodCall ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
MethodChannel ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
MutableMap ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  PackageManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  SecurityException ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Settings ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  String ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  Suppress ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  activityManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  contains ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  contentResolver ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	emptyList ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	filterNot ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getCONTAINS ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getContains ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getEMPTYList ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getEmptyList ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getFILTERNot ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getFilterNot ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	getLISTOf ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  	getListOf ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getMAP ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getMap ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getSET ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
getSTARTSWith ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getSet ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
getStartsWith ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  getSystemFeatures ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
isEmulator ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  listOf ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  map ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  packageManager ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  set ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
startsWith ;dev.fluttercommunity.plus.device_info.MethodCallHandlerImpl  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  Context 	java.lang  HashMap 	java.lang  MethodCallHandlerImpl 	java.lang  
MethodChannel 	java.lang  SecurityException 	java.lang  Settings 	java.lang  contains 	java.lang  	emptyList 	java.lang  	filterNot 	java.lang  listOf 	java.lang  map 	java.lang  set 	java.lang  
startsWith 	java.lang  HashMap 	java.util  Any kotlin  Array kotlin  Boolean kotlin  Build kotlin  Context kotlin  	Function1 kotlin  HashMap kotlin  Int kotlin  MethodCallHandlerImpl kotlin  
MethodChannel kotlin  Nothing kotlin  SecurityException kotlin  Settings kotlin  String kotlin  Suppress kotlin  contains kotlin  	emptyList kotlin  	filterNot kotlin  listOf kotlin  map kotlin  set kotlin  
startsWith kotlin  getFILTERNot kotlin.Array  getFilterNot kotlin.Array  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getSTARTSWith 
kotlin.String  
getStartsWith 
kotlin.String  Build kotlin.annotation  Context kotlin.annotation  HashMap kotlin.annotation  MethodCallHandlerImpl kotlin.annotation  
MethodChannel kotlin.annotation  SecurityException kotlin.annotation  Settings kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  	filterNot kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  set kotlin.annotation  
startsWith kotlin.annotation  Build kotlin.collections  Context kotlin.collections  HashMap kotlin.collections  List kotlin.collections  MethodCallHandlerImpl kotlin.collections  
MethodChannel kotlin.collections  
MutableMap kotlin.collections  SecurityException kotlin.collections  Settings kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  	filterNot kotlin.collections  listOf kotlin.collections  map kotlin.collections  set kotlin.collections  
startsWith kotlin.collections  getMAP kotlin.collections.List  getMap kotlin.collections.List  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  Build kotlin.comparisons  Context kotlin.comparisons  HashMap kotlin.comparisons  MethodCallHandlerImpl kotlin.comparisons  
MethodChannel kotlin.comparisons  SecurityException kotlin.comparisons  Settings kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  	filterNot kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  set kotlin.comparisons  
startsWith kotlin.comparisons  Build 	kotlin.io  Context 	kotlin.io  HashMap 	kotlin.io  MethodCallHandlerImpl 	kotlin.io  
MethodChannel 	kotlin.io  SecurityException 	kotlin.io  Settings 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  	filterNot 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  set 	kotlin.io  
startsWith 	kotlin.io  Build 
kotlin.jvm  Context 
kotlin.jvm  HashMap 
kotlin.jvm  MethodCallHandlerImpl 
kotlin.jvm  
MethodChannel 
kotlin.jvm  SecurityException 
kotlin.jvm  Settings 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  	filterNot 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  set 
kotlin.jvm  
startsWith 
kotlin.jvm  Build 
kotlin.ranges  Context 
kotlin.ranges  HashMap 
kotlin.ranges  MethodCallHandlerImpl 
kotlin.ranges  
MethodChannel 
kotlin.ranges  SecurityException 
kotlin.ranges  Settings 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  	filterNot 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  set 
kotlin.ranges  
startsWith 
kotlin.ranges  Build kotlin.sequences  Context kotlin.sequences  HashMap kotlin.sequences  MethodCallHandlerImpl kotlin.sequences  
MethodChannel kotlin.sequences  SecurityException kotlin.sequences  Settings kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  	filterNot kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  set kotlin.sequences  
startsWith kotlin.sequences  Build kotlin.text  Context kotlin.text  HashMap kotlin.text  MethodCallHandlerImpl kotlin.text  
MethodChannel kotlin.text  SecurityException kotlin.text  Settings kotlin.text  contains kotlin.text  	emptyList kotlin.text  	filterNot kotlin.text  listOf kotlin.text  map kotlin.text  set kotlin.text  
startsWith kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             