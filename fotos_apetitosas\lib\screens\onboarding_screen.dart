import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../theme/colors.dart';
import '../theme/app_theme.dart';

/// Tela de onboarding educativo para novos usuários
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = false;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Transforme seus doces em arte',
      description: 'Aprenda a fotografar seus doces como um profissional com nossas guias inteligentes',
      icon: Icons.camera_alt_outlined,
      color: AppColors.primary,
    ),
    OnboardingPage(
      title: 'Aprenda enquanto fotografa',
      description: 'Receba dicas em tempo real sobre composição, iluminação e enquadramento',
      icon: Icons.school_outlined,
      color: AppColors.secondary,
    ),
    OnboardingPage(
      title: 'Dicas profissionais em tempo real',
      description: 'Nosso assistente inteligente analisa suas fotos e sugere melhorias',
      icon: Icons.lightbulb_outline,
      color: AppColors.accent,
    ),
    OnboardingPage(
      title: 'Edição simples e poderosa',
      description: 'Presets otimizados especialmente para doces e ferramentas de edição intuitivas',
      icon: Icons.tune_outlined,
      color: AppColors.primary,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header com logo e skip
            _buildHeader(),
            
            // Conteúdo das páginas
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            
            // Indicadores de página
            _buildPageIndicators(),
            
            // Botões de navegação
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo placeholder
          Text(
            'FotosApetitosas',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          
          // Skip button
          if (_currentPage < _pages.length - 1)
            TextButton(
              onPressed: () => _skipToEnd(),
              child: const Text('Pular'),
            ),
        ],
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Ícone animado
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.color,
            ),
          )
              .animate()
              .scale(duration: 600.ms, curve: Curves.elasticOut)
              .fadeIn(duration: 400.ms),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Título
          Text(
            page.title,
            style: Theme.of(context).textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.onBackground,
            ),
            textAlign: TextAlign.center,
          )
              .animate()
              .slideY(begin: 0.3, duration: 600.ms, curve: Curves.easeOut)
              .fadeIn(duration: 600.ms, delay: 200.ms),
          
          const SizedBox(height: AppSpacing.md),
          
          // Descrição
          Text(
            page.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.onSurfaceVariant,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          )
              .animate()
              .slideY(begin: 0.3, duration: 600.ms, curve: Curves.easeOut)
              .fadeIn(duration: 600.ms, delay: 400.ms),
        ],
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: _currentPage == index ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentPage == index
                  ? AppColors.primary
                  : AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(4),
            ),
          )
              .animate(target: _currentPage == index ? 1 : 0)
              .scaleX(duration: 300.ms, curve: Curves.easeInOut),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          // Botão Voltar
          if (_currentPage > 0)
            Expanded(
              child: TextButton(
                onPressed: _previousPage,
                child: const Text('Voltar'),
              ),
            ),
          
          if (_currentPage > 0) const SizedBox(width: AppSpacing.md),
          
          // Botão Próximo/Começar
          Expanded(
            flex: _currentPage == 0 ? 1 : 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextPage,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(_currentPage == _pages.length - 1 ? 'Começar' : 'Próximo'),
            ),
          ),
        ],
      ),
    );
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  void _skipToEnd() {
    _pageController.animateToPage(
      _pages.length - 1,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _finishOnboarding() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Solicitar permissões
      await _requestPermissions();
      
      // Marcar onboarding como concluído
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed', true);
      
      // Navegar para a tela principal
      if (mounted) {
        context.go('/');
      }
    } catch (e) {
      // Tratar erro de permissões
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao solicitar permissões: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _requestPermissions() async {
    // Solicitar permissão da câmera
    final cameraStatus = await Permission.camera.request();
    
    // Solicitar permissão da galeria
    final storageStatus = await Permission.photos.request();
    
    if (cameraStatus.isDenied || storageStatus.isDenied) {
      throw Exception('Permissões necessárias não foram concedidas');
    }
  }
}

/// Modelo para as páginas do onboarding
class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
